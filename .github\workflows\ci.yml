name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.5'
        channel: 'stable'
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Verify formatting
      run: dart format --output=none --set-exit-if-changed .
    
    - name: Analyze project source
      run: flutter analyze
    
    - name: Run tests
      run: flutter test

  build-android:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.5'
        channel: 'stable'
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Build APK (Dev)
      if: github.ref == 'refs/heads/develop'
      run: flutter build apk --flavor dev --target lib/main_dev.dart
    
    - name: Build APK (Prod)
      if: github.ref == 'refs/heads/main'
      run: flutter build apk --flavor prod --target lib/main_prod.dart --release
    
    - name: Upload APK
      uses: actions/upload-artifact@v4
      with:
        name: android-apk
        path: build/app/outputs/flutter-apk/*.apk

  build-ios:
    needs: test
    runs-on: macos-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.5'
        channel: 'stable'
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Build iOS (Dev)
      if: github.ref == 'refs/heads/develop'
      run: |
        cd ios
        pod install
        cd ..
        flutter build ios --flavor dev --target lib/main_dev.dart --no-codesign
    
    - name: Build iOS (Prod)
      if: github.ref == 'refs/heads/main'
      run: |
        cd ios
        pod install
        cd ..
        flutter build ios --flavor prod --target lib/main_prod.dart --release --no-codesign
    
    - name: Upload iOS Build
      uses: actions/upload-artifact@v4
      with:
        name: ios-build
        path: build/ios/iphoneos/*.app
