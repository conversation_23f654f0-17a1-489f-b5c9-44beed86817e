name: Deploy to Stores

on:
  release:
    types: [published]

jobs:
  deploy-android:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'zulu'
        java-version: '17'
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.5'
        channel: 'stable'
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Decode Keystore
      env:
        ENCODED_STRING: ${{ secrets.KEYSTORE_BASE64 }}
      run: |
        echo $ENCODED_STRING | base64 -di > android/app/keystore.jks
    
    - name: Build App Bundle
      run: flutter build appbundle --flavor prod --target lib/main_prod.dart --release
      env:
        KEYSTORE_PASSWORD: ${{ secrets.KEYSTORE_PASSWORD }}
        KEY_ALIAS: ${{ secrets.KEY_ALIAS }}
        KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}
    
    - name: Deploy to Play Store
      uses: r0adkll/upload-google-play@v1
      with:
        serviceAccountJsonPlainText: ${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_JSON }}
        packageName: com.mdd.mddplusapp
        releaseFiles: build/app/outputs/bundle/prodRelease/app-prod-release.aab
        track: production
        status: completed

  deploy-ios:
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.24.5'
        channel: 'stable'
    
    - name: Get dependencies
      run: flutter pub get
    
    - name: Setup Xcode
      uses: maxim-lobanov/setup-xcode@v1
      with:
        xcode-version: latest-stable
    
    - name: Import Code-Signing Certificates
      uses: Apple-Actions/import-codesign-certs@v2
      with:
        p12-file-base64: ${{ secrets.IOS_CERTIFICATES_P12 }}
        p12-password: ${{ secrets.IOS_CERTIFICATES_PASSWORD }}
    
    - name: Download Provisioning Profiles
      uses: Apple-Actions/download-provisioning-profiles@v1
      with:
        bundle-id: com.mdd.mddplusapp
        issuer-id: ${{ secrets.APPSTORE_ISSUER_ID }}
        api-key-id: ${{ secrets.APPSTORE_KEY_ID }}
        api-private-key: ${{ secrets.APPSTORE_PRIVATE_KEY }}
    
    - name: Build iOS
      run: |
        cd ios
        pod install
        cd ..
        flutter build ios --flavor prod --target lib/main_prod.dart --release
    
    - name: Build and Upload to App Store
      uses: Apple-Actions/upload-testflight-build@v1
      with:
        app-path: build/ios/iphoneos/Runner.app
        issuer-id: ${{ secrets.APPSTORE_ISSUER_ID }}
        api-key-id: ${{ secrets.APPSTORE_KEY_ID }}
        api-private-key: ${{ secrets.APPSTORE_PRIVATE_KEY }}
