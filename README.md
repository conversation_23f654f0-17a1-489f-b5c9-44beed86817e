# MDD Plus App

A comprehensive financial management mobile application built with Flutter, featuring clean architecture, multi-language support, and modern UI/UX design.

## Features

### 🏗️ Architecture
- **Clean Architecture** with separation of concerns
- **Bloc/Cubit** for state management
- **Dependency Injection** with GetIt
- **Repository Pattern** for data management
- **Use Cases** for business logic

### 🌐 Internationalization
- **Multi-language support** (English & Arabic)
- **RTL/LTR** layout support
- **Dynamic language switching**
- **Localized date/time formatting**

### 🎨 UI/UX
- **Material Design 3** components
- **Dark/Light theme** support
- **Responsive design** for all screen sizes
- **Custom animations** and transitions
- **Accessibility** features

### 🔐 Security
- **Biometric authentication** (Face ID, Fingerprint)
- **Secure storage** for sensitive data
- **Token-based authentication**
- **Data encryption**

### 🔥 Firebase Integration
- **Firebase Analytics** for user insights
- **Firebase Messaging** for push notifications
- **Firebase Crashlytics** for crash reporting
- **Firebase Remote Config** for feature flags

### 📊 Error Monitoring
- **Sentry integration** for error tracking
- **Comprehensive logging**
- **Performance monitoring**
- **User feedback collection**

### 🚀 DevOps
- **CI/CD pipelines** with GitHub Actions
- **Automated testing**
- **Code quality checks**
- **Multi-environment support** (Dev/Prod)

## Getting Started

### Prerequisites

- Flutter SDK (3.24.5 or later)
- Dart SDK (3.4.3 or later)
- Android Studio / VS Code
- Xcode (for iOS development)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/mdd_plus_app.git
   cd mdd_plus_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Configure Firebase**
   - Add your `google-services.json` to `android/app/`
   - Add your `GoogleService-Info.plist` to `ios/Runner/`
   - Update `firebase_options.dart` with your configuration

5. **Configure Sentry**
   - Update `config/app_config.json` with your Sentry DSN

6. **Run the app**
   ```bash
   # Development
   flutter run --flavor dev --target lib/main_dev.dart
   
   # Production
   flutter run --flavor prod --target lib/main_prod.dart
   ```

## Project Structure

```
lib/
├── core/                   # Core functionality
│   ├── blocProviders/      # Bloc providers setup
│   ├── cubits/            # Global cubits (theme, locale)
│   ├── endPoints/         # API endpoints
│   ├── errors/            # Error handling
│   ├── exceptions/        # Custom exceptions
│   ├── injection/         # Dependency injection
│   ├── interceptor/       # HTTP interceptors
│   ├── models/            # Core models
│   ├── network/           # Network utilities
│   ├── provider/          # Bloc observer
│   ├── repositories/      # Core repositories
│   ├── usecases/          # Base use cases
│   └── widgets/           # Reusable widgets
├── features/              # Feature modules
│   └── auth/              # Authentication feature
│       ├── data/          # Data layer
│       ├── domain/        # Domain layer
│       └── presentation/  # Presentation layer
├── l10n/                  # Localization files
├── services/              # External services
├── theme/                 # App theming
├── utils/                 # Utility functions
├── main.dart              # Main entry point
├── main_dev.dart          # Development entry
├── main_prod.dart         # Production entry
└── main_common.dart       # Common initialization
```

## Configuration

### Environment Variables

Create configuration files in the `config/` directory:

**app_config.json**
```json
{
  "sentryDns": "your-sentry-dsn",
  "googleMapKeyAndroid": "your-android-key",
  "googleMapKeyIos": "your-ios-key"
}
```

### Build Flavors

The app supports multiple build flavors:

- **dev**: Development environment
- **prod**: Production environment

### Scripts

Common development scripts:

```bash
# Run tests
flutter test

# Generate code
flutter packages pub run build_runner build --delete-conflicting-outputs

# Analyze code
flutter analyze

# Format code
dart format .

# Build APK
flutter build apk --flavor prod --target lib/main_prod.dart

# Build iOS
flutter build ios --flavor prod --target lib/main_prod.dart
```

## Testing

### Unit Tests
```bash
flutter test
```

### Integration Tests
```bash
flutter drive --target=test_driver/app.dart
```

### Widget Tests
```bash
flutter test test/widget_test.dart
```

## Deployment

### Android
1. Configure signing in `android/app/build.gradle`
2. Build release APK: `flutter build apk --release`
3. Upload to Google Play Console

### iOS
1. Configure signing in Xcode
2. Build release: `flutter build ios --release`
3. Archive and upload to App Store Connect

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style

- Follow [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use meaningful variable and function names
- Add comments for complex logic
- Write tests for new features

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation

## Acknowledgments

- Flutter team for the amazing framework
- Firebase for backend services
- Sentry for error monitoring
- All contributors and maintainers
