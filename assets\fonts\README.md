# Fonts Directory

This directory contains all the font assets for the MDD Plus App.

## Required Fonts

### Primary Font Family: DinNextLt
- `din-next-lt-w23-light.ttf` - Light weight (300)
- `din-next-lt-w23-regular.ttf` - Regular weight (400)
- `din-next-lt-w23-medium.ttf` - Medium weight (500)
- `din-next-lt-w23-bold.ttf` - Bold weight (700)

### Arabic Font Support
The DinNextLt font family includes Arabic character support, making it suitable for both English and Arabic text.

## Font Usage Guidelines

### Font Weights
- **Light (300)**: Use for secondary text, captions, and subtle elements
- **Regular (400)**: Use for body text, descriptions, and general content
- **Medium (500)**: Use for emphasized text, labels, and important information
- **Bold (700)**: Use for headings, titles, and primary actions

### Font Sizes
- **Display**: 32px+ (Headings, hero text)
- **Title**: 24-28px (Page titles, section headers)
- **Headline**: 20-22px (Card titles, important labels)
- **Body**: 16-18px (Main content, descriptions)
- **Caption**: 14px (Secondary information, metadata)
- **Small**: 12px (Fine print, disclaimers)

### Line Heights
- **Display**: 1.2 (tight for large text)
- **Title**: 1.3 (slightly tight for headers)
- **Body**: 1.5 (comfortable for reading)
- **Caption**: 1.4 (compact for small text)

## Implementation

The fonts are configured in `pubspec.yaml`:

```yaml
flutter:
  fonts:
    - family: DinNextLt
      fonts:
        - asset: assets/fonts/din-next-lt-w23-light.ttf
          weight: 300
        - asset: assets/fonts/din-next-lt-w23-regular.ttf
          weight: 400
        - asset: assets/fonts/din-next-lt-w23-medium.ttf
          weight: 500
        - asset: assets/fonts/din-next-lt-w23-bold.ttf
          weight: 700
```

## Usage in Code

```dart
Text(
  'Hello World',
  style: TextStyle(
    fontFamily: 'DinNextLt',
    fontWeight: FontWeight.w400, // Regular
    fontSize: 16,
  ),
)
```

## Accessibility

- Ensure minimum font size of 12px for readability
- Use sufficient contrast ratios (4.5:1 for normal text, 3:1 for large text)
- Support dynamic type scaling for accessibility
- Test with different font sizes and zoom levels

## Performance

- Fonts are loaded on app startup
- Only include necessary font weights to reduce app size
- Consider using system fonts as fallbacks
- Optimize font files for mobile delivery

## Licensing

Ensure proper licensing for the DinNextLt font family. If using custom fonts, verify commercial usage rights and include appropriate license files.

## Fallback Fonts

In case the primary font fails to load:
- **iOS**: San Francisco (SF Pro)
- **Android**: Roboto
- **Web**: System fonts (Arial, Helvetica, sans-serif)

## Testing

Test fonts across:
- Different devices and screen sizes
- Various operating systems
- Light and dark themes
- Different languages (English and Arabic)
- Accessibility settings (large text, bold text)
