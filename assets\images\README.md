# Images Directory

This directory contains all the image assets for the MDD Plus App.

## Required Images

### App Icons
- `app_icon.png` - Main app icon (1024x1024)
- `app_icon_rounded.png` - Rounded app icon for Android (1024x1024)

### Splash Screen
- `splash_logo.png` - Logo for splash screen (512x512)
- `splash_background.png` - Background image for splash screen
- `splash_image.png` - Main illustration for splash screen

### Onboarding
- `onboarding_1.png` - First onboarding screen illustration
- `onboarding_2.png` - Second onboarding screen illustration
- `onboarding_3.png` - Third onboarding screen illustration

### Authentication
- `login_illustration.png` - Login screen illustration
- `register_illustration.png` - Register screen illustration
- `forgot_password_illustration.png` - Forgot password illustration

### Dashboard
- `dashboard_hero.png` - Hero image for dashboard
- `empty_state.png` - Empty state illustration
- `no_data.png` - No data illustration
- `error_illustration.png` - Error state illustration

### Features
- `wallet_icon.png` - Wallet feature icon
- `investment_icon.png` - Investment feature icon
- `transaction_icon.png` - Transaction feature icon
- `report_icon.png` - Report feature icon

### Placeholders
- `placeholder_avatar.png` - Default user avatar
- `placeholder_image.png` - Generic image placeholder
- `placeholder_document.png` - Document placeholder

## Image Guidelines

- Use PNG format for images with transparency
- Use JPG format for photos and complex images
- Optimize images for mobile devices
- Provide @2x and @3x versions for iOS
- Use vector graphics (SVG) when possible for icons
- Maximum file size: 2MB per image
- Recommended dimensions are multiples of 8px

## Naming Convention

- Use lowercase letters
- Use underscores to separate words
- Be descriptive but concise
- Include size suffix when needed (e.g., `icon_24.png`)

## Organization

```
images/
├── icons/
│   ├── app_icon.png
│   ├── feature_icons/
│   └── ui_icons/
├── illustrations/
│   ├── onboarding/
│   ├── auth/
│   └── empty_states/
├── backgrounds/
├── avatars/
└── placeholders/
```
