version: '3.8'

services:
  app:
    build: .
    ports:
      - "8080:80"
    environment:
      - FLUTTER_ENV=production
    volumes:
      - ./config:/app/config:ro
    restart: unless-stopped
    
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - FLUTTER_ENV=development
    volumes:
      - .:/app
      - /app/node_modules
    restart: unless-stopped
    command: flutter run -d web-server --web-port 3000 --web-hostname 0.0.0.0
