import 'package:flutter_bloc/flutter_bloc.dart';

import '../cubits/localeCubit/locale_cubit.dart';
import '../cubits/themeCubit/theme_cubit.dart';

class BlocProviders {
  static List<BlocProvider> getProviders() {
    return [
      BlocProvider<LocaleCubit>(create: (_) => LocaleCubit()),
      BlocProvider<ThemeCubit>(create: (_) => ThemeCubit()),
      // Add more bloc providers here as needed
    ];
  }
}
