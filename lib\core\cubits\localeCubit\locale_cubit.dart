import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../injection/injection_container.dart';
import '../../../utils/constants.dart';

part 'locale_state.dart';

class LocaleCubit extends Cubit<LocaleState> {
  LocaleCubit() : super(LocaleInitial());

  static LocaleCubit get(BuildContext context) =>
      BlocProvider.of<LocaleCubit>(context);

  static const List<Locale> all = [
    Locale('ar'),
    Locale('en'),
  ];

  Locale get locale {
    return getIt<SharedPreferences>().getBool(PrefKeys.isEn) ?? false
        ? const Locale('en')
        : const Locale('ar');
  }

  void setLocale(Locale locale) {
    if (!all.contains(locale)) return;
    getIt<SharedPreferences>()
        .setBool(PrefKeys.isEn, 'en' == locale.languageCode);
    emit(LocaleChanged());
  }
}
