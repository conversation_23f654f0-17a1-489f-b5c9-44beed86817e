import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../injection/injection_container.dart';
import '../../../utils/constants.dart';
import '../../../utils/enums.dart' as app_enums;

part 'theme_state.dart';

class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit() : super(ThemeInitial());

  static ThemeCubit get(BuildContext context) =>
      BlocProvider.of<ThemeCubit>(context);

  app_enums.ThemeMode get themeMode {
    final isDarkMode = getIt<SharedPreferences>().getBool(PrefKeys.isDarkMode);
    if (isDarkMode == null) {
      return app_enums.ThemeMode.system;
    }
    return isDarkMode ? app_enums.ThemeMode.dark : app_enums.ThemeMode.light;
  }

  bool get isDarkMode {
    final savedMode = getIt<SharedPreferences>().getBool(PrefKeys.isDarkMode);
    if (savedMode == null) {
      // System mode - check system brightness
      return WidgetsBinding.instance.platformDispatcher.platformBrightness == 
          Brightness.dark;
    }
    return savedMode;
  }

  void setThemeMode(app_enums.ThemeMode mode) {
    switch (mode) {
      case app_enums.ThemeMode.light:
        getIt<SharedPreferences>().setBool(PrefKeys.isDarkMode, false);
        break;
      case app_enums.ThemeMode.dark:
        getIt<SharedPreferences>().setBool(PrefKeys.isDarkMode, true);
        break;
      case app_enums.ThemeMode.system:
        getIt<SharedPreferences>().remove(PrefKeys.isDarkMode);
        break;
    }
    emit(ThemeChanged());
  }

  void toggleTheme() {
    final currentMode = themeMode;
    if (currentMode == app_enums.ThemeMode.light) {
      setThemeMode(app_enums.ThemeMode.dark);
    } else {
      setThemeMode(app_enums.ThemeMode.light);
    }
  }
}
