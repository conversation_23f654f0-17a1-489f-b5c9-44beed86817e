class EndPoints {
  // Auth endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';
  static const String changePassword = '/auth/change-password';
  
  // User endpoints
  static const String profile = '/user/profile';
  static const String updateProfile = '/user/update-profile';
  static const String deleteAccount = '/user/delete-account';
  
  // OTP endpoints
  static const String createOtp = '/otp/create';
  static const String validateOtp = '/otp/validate';
  static const String validatePublicOtp = '/otp/validate-public';
  
  // Notifications endpoints
  static const String notifications = '/notifications';
  static const String markAsRead = '/notifications/mark-as-read';
  
  // Settings endpoints
  static const String settings = '/settings';
  static const String updateSettings = '/settings/update';
}
