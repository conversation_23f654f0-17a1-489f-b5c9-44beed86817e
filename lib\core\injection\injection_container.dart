import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_it/get_it.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:local_auth/local_auth.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../services/dio_client.dart';
import '../../services/fcm.dart';
import '../network/network_info.dart';
import '../repositories/device_info_repository.dart';
import '../repositories/token_repository.dart';

final getIt = GetIt.instance;

Future<void> initGetItSetup() async {
  // Check if already initialized
  if (getIt.isRegistered<NetworkInfo>()) {
    return;
  }

  //===================== Core  =====================
  getIt.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(getIt()));

  getIt.registerLazySingleton<TokenRepository>(
    () => TokenRepositoryImpl(getIt()),
  );

  getIt.registerLazySingleton<DeviceInfoRepository>(
    () => DeviceInfoRepositoryImpl(getIt()),
  );

  getIt.registerLazySingleton<DioClient>(
    () => DioClient(getIt(), getIt(), getIt(), getIt()),
  );

  //===================== External  =====================
  final packageInfo = await PackageInfo.fromPlatform();
  getIt.registerLazySingleton(() => packageInfo);
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerLazySingleton(() => sharedPreferences);
  getIt.registerLazySingleton(() => FirebaseCloudMessaging());
  getIt.registerLazySingleton(() => const FlutterSecureStorage());
  getIt.registerLazySingleton(() => LocalAuthentication());
  getIt.registerLazySingleton(() => InternetConnectionChecker());
  getIt.registerLazySingleton(() => Dio());
}
