import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../repositories/device_info_repository.dart';
import '../repositories/token_repository.dart';
import '../../utils/constants.dart';

class AppInterceptors extends Interceptor {
  final TokenRepository _tokenRepo;
  final DeviceInfoRepository _deviceInfoRepo;
  final SharedPreferences _prefs;

  AppInterceptors(this._tokenRepo, this._deviceInfoRepo, this._prefs);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Add authorization header
    _tokenRepo.getToken().then((token) {
      if (token != null) {
        options.headers['Authorization'] = 'Bearer $token';
      }
    });

    // Add device info headers
    options.headers['Device-Id'] = _deviceInfoRepo.deviceId;
    options.headers['Device-Name'] = _deviceInfoRepo.deviceName;
    options.headers['Device-Model'] = _deviceInfoRepo.deviceModel;
    options.headers['OS-Version'] = _deviceInfoRepo.osVersion;
    options.headers['App-Version'] = _deviceInfoRepo.appVersion;
    options.headers['Platform-Type'] = _deviceInfoRepo.platformType.index;

    // Add user info if available
    final userId = _prefs.getString(PrefKeys.userID);
    if (userId != null) {
      options.headers['User-Id'] = userId;
    }

    // Add language header
    final isEn = _prefs.getBool(PrefKeys.isEn) ?? false;
    options.headers['Accept-Language'] = isEn ? 'en' : 'ar';

    log('Request: ${options.method} ${options.uri}');
    log('Headers: ${options.headers}');
    
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    log('Response: ${response.statusCode} ${response.requestOptions.uri}');
    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    log('Error: ${err.response?.statusCode} ${err.requestOptions.uri}');
    log('Error message: ${err.message}');
    
    // Handle unauthorized errors
    if (err.response?.statusCode == 401) {
      _tokenRepo.deleteUserAccess();
    }
    
    super.onError(err, handler);
  }
}
