import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../l10n/app_localizations.dart';
import '../theme/themes.dart';
import '../utils/enums.dart' as utils;
import 'blocProviders/bloc_providers.dart';
import 'cubits/localeCubit/locale_cubit.dart';
import 'cubits/themeCubit/theme_cubit.dart';
import 'splash_screen.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    log('Building MyApp with flavor: ${FlavorConfig.instance.name}');

    return MultiBlocProvider(
      providers: BlocProviders.getProviders(),
      child: BlocBuilder<LocaleCubit, LocaleState>(
        builder: (context, localeState) {
          return BlocBuilder<ThemeCubit, ThemeState>(
            builder: (context, themeState) {
              final locale = LocaleCubit.get(context).locale;
              final isDarkMode = ThemeCubit.get(context).isDarkMode;

              return GestureDetector(
                onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
                child: MaterialApp(
                  title: 'MDD Plus',
                  locale: locale,
                  navigatorObservers: [SentryNavigatorObserver()],
                  debugShowCheckedModeBanner: false,
                  navigatorKey: navigatorKey,
                  localizationsDelegates:
                      AppLocalizations.localizationsDelegates,
                  supportedLocales: AppLocalizations.supportedLocales,
                  builder: EasyLoading.init(),
                  theme: AppThemes.getThemeData(locale, isDarkMode),
                  darkTheme: AppThemes.getDarkTheme(locale),
                  themeMode: _getThemeMode(context),
                  home: const SplashScreen(),
                ),
              );
            },
          );
        },
      ),
    );
  }

  ThemeMode _getThemeMode(BuildContext context) {
    final themeCubit = ThemeCubit.get(context);
    switch (themeCubit.themeMode) {
      case utils.ThemeMode.light:
        return ThemeMode.light;
      case utils.ThemeMode.dark:
        return ThemeMode.dark;
      case utils.ThemeMode.system:
        return ThemeMode.system;
    }
  }
}

/// Configure EasyLoading
void configLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.fadingCircle
    ..loadingStyle = EasyLoadingStyle.dark
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..progressColor = Colors.yellow
    ..backgroundColor = Colors.green
    ..indicatorColor = Colors.yellow
    ..textColor = Colors.yellow
    ..maskColor = Colors.blue.withOpacity(0.5)
    ..userInteractions = true
    ..dismissOnTap = false;
}
