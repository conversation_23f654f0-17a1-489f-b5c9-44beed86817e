import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../utils/enums.dart';

abstract class DeviceInfoRepository {
  Future<void> init();
  String get deviceId;
  String get deviceName;
  String get deviceModel;
  String get osVersion;
  String get appVersion;
  String get appBuildNumber;
  PlatformTypes get platformType;
}

class DeviceInfoRepositoryImpl implements DeviceInfoRepository {
  final PackageInfo _packageInfo;
  
  late String _deviceId;
  late String _deviceName;
  late String _deviceModel;
  late String _osVersion;

  DeviceInfoRepositoryImpl(this._packageInfo);

  @override
  Future<void> init() async {
    final deviceInfo = DeviceInfoPlugin();
    
    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      _deviceId = androidInfo.id;
      _deviceName = androidInfo.device;
      _deviceModel = androidInfo.model;
      _osVersion = androidInfo.version.release;
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      _deviceId = iosInfo.identifierForVendor ?? '';
      _deviceName = iosInfo.name;
      _deviceModel = iosInfo.model;
      _osVersion = iosInfo.systemVersion;
    }
  }

  @override
  String get deviceId => _deviceId;

  @override
  String get deviceName => _deviceName;

  @override
  String get deviceModel => _deviceModel;

  @override
  String get osVersion => _osVersion;

  @override
  String get appVersion => _packageInfo.version;

  @override
  String get appBuildNumber => _packageInfo.buildNumber;

  @override
  PlatformTypes get platformType {
    if (Platform.isAndroid) {
      return PlatformTypes.android;
    } else if (Platform.isIOS) {
      return PlatformTypes.ios;
    } else {
      return PlatformTypes.web;
    }
  }
}
