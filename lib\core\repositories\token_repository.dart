import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../../utils/constants.dart';
import '../../utils/sentry_reporter.dart';

abstract class TokenRepository {
  Future<void> saveUserAccess({
    required String token,
    required String email,
    required String password,
  });
  Future<Map<String, String>?> readUserAccess();
  Future<void> deleteUserAccess();
  Future<String?> getToken();
}

class TokenRepositoryImpl implements TokenRepository {
  final FlutterSecureStorage _secureStorage;

  TokenRepositoryImpl(this._secureStorage);

  @override
  Future<void> saveUserAccess({
    required String token,
    required String email,
    required String password,
  }) async {
    try {
      await _secureStorage.write(key: SecureStorageKeys.token, value: token);
      await _secureStorage.write(key: SecureStorageKeys.email, value: email);
      await _secureStorage.write(
        key: SecureStorageKeys.password,
        value: password,
      );
    } on PlatformException {
      await SentryReporter.genericThrow(
        'Error saving token to secure storage',
        stackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<void> deleteUserAccess() async {
    try {
      await _secureStorage.delete(key: SecureStorageKeys.token);
      await _secureStorage.delete(key: SecureStorageKeys.email);
      await _secureStorage.delete(key: SecureStorageKeys.password);
    } on PlatformException {
      await SentryReporter.genericThrow(
        'Error deleting token from secure storage',
        stackTrace: StackTrace.current,
      );
    }
  }

  @override
  Future<String?> getToken() async {
    try {
      return await _secureStorage.read(key: SecureStorageKeys.token);
    } on PlatformException {
      await deleteUserAccess();
      await SentryReporter.genericThrow(
        'Error reading token from secure storage',
        stackTrace: StackTrace.current,
      );
      return null;
    }
  }

  /// Read token from local storage.
  @override
  Future<Map<String, String>?> readUserAccess() async {
    try {
      final token = await _secureStorage.read(key: SecureStorageKeys.token);
      final email = await _secureStorage.read(key: SecureStorageKeys.email);
      final pass = await _secureStorage.read(key: SecureStorageKeys.password);
      if (token == null || email == null || pass == null) {
        return null;
      }

      return {
        SecureStorageKeys.token: token,
        SecureStorageKeys.email: email,
        SecureStorageKeys.password: pass,
      };
    } on PlatformException {
      await deleteUserAccess();
      await SentryReporter.genericThrow(
        'Error reading token from secure storage',
        stackTrace: StackTrace.current,
      );
      return null;
    }
  }
}
