import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../features/auth/presentation/pages/login_screen.dart';
import '../services/fcm.dart';
import '../utils/extensions.dart';
import 'injection/injection_container.dart';
import 'repositories/device_info_repository.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _scaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _animationController.forward();
  }

  void _initializeApp() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        // Register FCM notifications
        getIt<FirebaseCloudMessaging>().registerNotification();

        // Initialize device info
        await getIt<DeviceInfoRepository>().init();

        // Wait for animations to complete
        await Future.delayed(const Duration(seconds: 3));

        // Navigate to login screen
        if (mounted) {
          context.pushReplacement(const LoginScreen());
        }
      } catch (e) {
        // Handle initialization errors
        if (mounted) {
          context.showSnackBar('Initialization failed: $e');
          // Still navigate to login after error
          Timer(const Duration(seconds: 2), () {
            if (mounted) {
              context.pushReplacement(const LoginScreen());
            }
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.backgroundColor,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // App Logo
                    Container(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        children: [
                          // MDD FinTech Logo
                          Container(
                            width: 160,
                            height: 80,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: context.primaryColor.withOpacity(0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: SvgPicture.asset(
                              'assets/svgs/mdd_logo_horizontal.svg',
                              fit: BoxFit.contain,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'MDD FinTech',
                            style: context.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: context.primaryColor,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Advanced Financial Technology Solutions',
                            style: context.textTheme.bodyLarge?.copyWith(
                              color: context.textTheme.bodyMedium?.color,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    // Splash Image or Illustration
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Image.asset(
                          'assets/images/splash-image.png',
                          width: double.infinity,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            // Fallback if image doesn't exist
                            return Icon(
                              Icons.business_center,
                              size: 200,
                              color: context.primaryColor.withOpacity(0.3),
                            );
                          },
                        ),
                      ),
                    ),

                    // Loading Indicator
                    Padding(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        children: [
                          SizedBox(
                            width: 40,
                            height: 40,
                            child: CircularProgressIndicator(
                              strokeWidth: 3,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                context.primaryColor,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Loading...',
                            style: context.textTheme.bodyMedium?.copyWith(
                              color: context.textTheme.bodyMedium?.color,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
