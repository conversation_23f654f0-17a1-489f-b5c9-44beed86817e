import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../utils/extensions.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;
  final VoidCallback? onBackPressed;

  const CustomAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: titleWidget ??
          (title != null
              ? Text(
                  title!,
                  style: context.textTheme.titleLarge?.copyWith(
                    color: foregroundColor ?? context.textTheme.titleLarge?.color,
                    fontWeight: FontWeight.w600,
                  ),
                )
              : null),
      actions: actions,
      leading: leading ??
          (automaticallyImplyLeading && context.canPop
              ? IconButton(
                  icon: SvgPicture.asset(
                    'assets/svgs/arrow_left.svg',
                    width: 24,
                    height: 24,
                    colorFilter: ColorFilter.mode(
                      foregroundColor ?? context.textTheme.titleLarge?.color ?? Colors.black,
                      BlendMode.srcIn,
                    ),
                  ),
                  onPressed: onBackPressed ?? () => context.pop(),
                )
              : null),
      automaticallyImplyLeading: false,
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Colors.transparent,
      foregroundColor: foregroundColor,
      elevation: elevation,
      scrolledUnderElevation: elevation,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class CustomSliverAppBar extends StatelessWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double expandedHeight;
  final Widget? flexibleSpace;
  final bool pinned;
  final bool floating;
  final bool snap;
  final VoidCallback? onBackPressed;

  const CustomSliverAppBar({
    super.key,
    this.title,
    this.titleWidget,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.expandedHeight = 200.0,
    this.flexibleSpace,
    this.pinned = true,
    this.floating = false,
    this.snap = false,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      title: titleWidget ??
          (title != null
              ? Text(
                  title!,
                  style: context.textTheme.titleLarge?.copyWith(
                    color: foregroundColor ?? context.textTheme.titleLarge?.color,
                    fontWeight: FontWeight.w600,
                  ),
                )
              : null),
      actions: actions,
      leading: leading ??
          (automaticallyImplyLeading && context.canPop
              ? IconButton(
                  icon: SvgPicture.asset(
                    'assets/svgs/arrow_left.svg',
                    width: 24,
                    height: 24,
                    colorFilter: ColorFilter.mode(
                      foregroundColor ?? context.textTheme.titleLarge?.color ?? Colors.black,
                      BlendMode.srcIn,
                    ),
                  ),
                  onPressed: onBackPressed ?? () => context.pop(),
                )
              : null),
      automaticallyImplyLeading: false,
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Colors.transparent,
      foregroundColor: foregroundColor,
      expandedHeight: expandedHeight,
      flexibleSpace: flexibleSpace,
      pinned: pinned,
      floating: floating,
      snap: snap,
      elevation: 0,
      scrolledUnderElevation: 0,
    );
  }
}
