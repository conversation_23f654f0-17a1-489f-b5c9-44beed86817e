// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'MDD FinTech';

  @override
  String get welcome => 'مرحباً';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get register => 'التسجيل';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get resetPassword => 'إعادة تعيين كلمة المرور';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get home => 'الرئيسية';

  @override
  String get dashboard => 'لوحة التحكم';

  @override
  String get statistics => 'الإحصائيات';

  @override
  String get reports => 'التقارير';

  @override
  String get transactions => 'المعاملات';

  @override
  String get investments => 'الاستثمارات';

  @override
  String get wallet => 'المحفظة';

  @override
  String get balance => 'الرصيد';

  @override
  String get deposit => 'إيداع';

  @override
  String get withdraw => 'سحب';

  @override
  String get transfer => 'تحويل';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get add => 'إضافة';

  @override
  String get search => 'بحث';

  @override
  String get filter => 'تصفية';

  @override
  String get sort => 'ترتيب';

  @override
  String get refresh => 'تحديث';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومات';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get ok => 'موافق';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get close => 'إغلاق';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get finish => 'إنهاء';

  @override
  String get skip => 'تخطي';

  @override
  String get done => 'تم';

  @override
  String get continueAction => 'متابعة';

  @override
  String get back => 'رجوع';

  @override
  String get submit => 'إرسال';

  @override
  String get send => 'إرسال';

  @override
  String get receive => 'استقبال';

  @override
  String get share => 'مشاركة';

  @override
  String get copy => 'نسخ';

  @override
  String get paste => 'لصق';

  @override
  String get cut => 'قص';

  @override
  String get select => 'اختيار';

  @override
  String get selectAll => 'اختيار الكل';

  @override
  String get clear => 'مسح';

  @override
  String get reset => 'إعادة تعيين';

  @override
  String get update => 'تحديث';

  @override
  String get upgrade => 'ترقية';

  @override
  String get download => 'تحميل';

  @override
  String get upload => 'رفع';

  @override
  String get import => 'استيراد';

  @override
  String get export => 'تصدير';

  @override
  String get print => 'طباعة';

  @override
  String get preview => 'معاينة';

  @override
  String get view => 'عرض';

  @override
  String get hide => 'إخفاء';

  @override
  String get show => 'إظهار';

  @override
  String get enable => 'تفعيل';

  @override
  String get disable => 'إلغاء التفعيل';

  @override
  String get activate => 'تنشيط';

  @override
  String get deactivate => 'إلغاء التنشيط';

  @override
  String get online => 'متصل';

  @override
  String get offline => 'غير متصل';

  @override
  String get connected => 'متصل';

  @override
  String get disconnected => 'منقطع';

  @override
  String get available => 'متاح';

  @override
  String get unavailable => 'غير متاح';

  @override
  String get active => 'نشط';

  @override
  String get inactive => 'غير نشط';

  @override
  String get pending => 'في الانتظار';

  @override
  String get approved => 'موافق عليه';

  @override
  String get rejected => 'مرفوض';

  @override
  String get completed => 'مكتمل';

  @override
  String get cancelled => 'ملغي';

  @override
  String get failed => 'فشل';

  @override
  String get expired => 'منتهي الصلاحية';

  @override
  String get valid => 'صالح';

  @override
  String get invalid => 'غير صالح';

  @override
  String get required => 'مطلوب';

  @override
  String get optional => 'اختياري';

  @override
  String get public => 'عام';

  @override
  String get private => 'خاص';

  @override
  String get personal => 'شخصي';

  @override
  String get business => 'تجاري';

  @override
  String get general => 'عام';

  @override
  String get advanced => 'متقدم';

  @override
  String get basic => 'أساسي';

  @override
  String get premium => 'مميز';

  @override
  String get free => 'مجاني';

  @override
  String get paid => 'مدفوع';

  @override
  String get trial => 'تجريبي';

  @override
  String get demo => 'تجريبي';

  @override
  String get beta => 'بيتا';

  @override
  String get alpha => 'ألفا';

  @override
  String get stable => 'مستقر';

  @override
  String get latest => 'الأحدث';

  @override
  String get newItem => 'جديد';

  @override
  String get old => 'قديم';

  @override
  String get recent => 'حديث';

  @override
  String get popular => 'شائع';

  @override
  String get featured => 'مميز';

  @override
  String get recommended => 'موصى به';

  @override
  String get trending => 'رائج';

  @override
  String get top => 'أعلى';

  @override
  String get bottom => 'أسفل';

  @override
  String get left => 'يسار';

  @override
  String get right => 'يمين';

  @override
  String get center => 'وسط';

  @override
  String get start => 'بداية';

  @override
  String get end => 'نهاية';

  @override
  String get first => 'الأول';

  @override
  String get last => 'الأخير';

  @override
  String get all => 'الكل';

  @override
  String get none => 'لا شيء';

  @override
  String get other => 'آخر';

  @override
  String get more => 'المزيد';

  @override
  String get less => 'أقل';

  @override
  String get today => 'اليوم';

  @override
  String get yesterday => 'أمس';

  @override
  String get tomorrow => 'غداً';

  @override
  String get thisWeek => 'هذا الأسبوع';

  @override
  String get lastWeek => 'الأسبوع الماضي';

  @override
  String get nextWeek => 'الأسبوع القادم';

  @override
  String get thisMonth => 'هذا الشهر';

  @override
  String get lastMonth => 'الشهر الماضي';

  @override
  String get nextMonth => 'الشهر القادم';

  @override
  String get thisYear => 'هذا العام';

  @override
  String get lastYear => 'العام الماضي';

  @override
  String get nextYear => 'العام القادم';

  @override
  String get date => 'التاريخ';

  @override
  String get time => 'الوقت';

  @override
  String get dateTime => 'التاريخ والوقت';

  @override
  String get day => 'يوم';

  @override
  String get week => 'أسبوع';

  @override
  String get month => 'شهر';

  @override
  String get year => 'سنة';

  @override
  String get hour => 'ساعة';

  @override
  String get minute => 'دقيقة';

  @override
  String get second => 'ثانية';

  @override
  String get am => 'ص';

  @override
  String get pm => 'م';

  @override
  String get language => 'اللغة';

  @override
  String get theme => 'المظهر';

  @override
  String get lightMode => 'الوضع الفاتح';

  @override
  String get darkMode => 'الوضع الداكن';

  @override
  String get systemMode => 'وضع النظام';

  @override
  String get fontSize => 'حجم الخط';

  @override
  String get small => 'صغير';

  @override
  String get medium => 'متوسط';

  @override
  String get large => 'كبير';

  @override
  String get extraLarge => 'كبير جداً';

  @override
  String get currency => 'العملة';

  @override
  String get amount => 'المبلغ';

  @override
  String get total => 'الإجمالي';

  @override
  String get subtotal => 'المجموع الفرعي';

  @override
  String get tax => 'الضريبة';

  @override
  String get discount => 'الخصم';

  @override
  String get fee => 'الرسوم';

  @override
  String get commission => 'العمولة';

  @override
  String get interest => 'الفائدة';

  @override
  String get profit => 'الربح';

  @override
  String get loss => 'الخسارة';

  @override
  String get income => 'الدخل';

  @override
  String get expense => 'المصروف';

  @override
  String get revenue => 'الإيرادات';

  @override
  String get cost => 'التكلفة';

  @override
  String get price => 'السعر';

  @override
  String get value => 'القيمة';

  @override
  String get rate => 'المعدل';

  @override
  String get percentage => 'النسبة المئوية';

  @override
  String get quantity => 'الكمية';

  @override
  String get count => 'العدد';

  @override
  String get number => 'الرقم';

  @override
  String get id => 'المعرف';

  @override
  String get name => 'الاسم';

  @override
  String get title => 'العنوان';

  @override
  String get description => 'الوصف';

  @override
  String get details => 'التفاصيل';

  @override
  String get summary => 'الملخص';

  @override
  String get notes => 'الملاحظات';

  @override
  String get comments => 'التعليقات';

  @override
  String get message => 'الرسالة';

  @override
  String get subject => 'الموضوع';

  @override
  String get content => 'المحتوى';

  @override
  String get body => 'النص';

  @override
  String get header => 'الرأس';

  @override
  String get footer => 'التذييل';

  @override
  String get sidebar => 'الشريط الجانبي';

  @override
  String get menu => 'القائمة';

  @override
  String get navigation => 'التنقل';

  @override
  String get breadcrumb => 'مسار التنقل';

  @override
  String get tab => 'علامة التبويب';

  @override
  String get page => 'الصفحة';

  @override
  String get section => 'القسم';

  @override
  String get category => 'الفئة';

  @override
  String get tag => 'العلامة';

  @override
  String get label => 'التسمية';

  @override
  String get badge => 'الشارة';

  @override
  String get icon => 'الأيقونة';

  @override
  String get image => 'الصورة';

  @override
  String get photo => 'الصورة الفوتوغرافية';

  @override
  String get picture => 'الصورة';

  @override
  String get video => 'الفيديو';

  @override
  String get audio => 'الصوت';

  @override
  String get file => 'الملف';

  @override
  String get document => 'الوثيقة';

  @override
  String get folder => 'المجلد';

  @override
  String get directory => 'الدليل';

  @override
  String get path => 'المسار';

  @override
  String get url => 'الرابط';

  @override
  String get link => 'الرابط';

  @override
  String get address => 'العنوان';

  @override
  String get location => 'الموقع';

  @override
  String get position => 'الموضع';

  @override
  String get coordinates => 'الإحداثيات';

  @override
  String get latitude => 'خط العرض';

  @override
  String get longitude => 'خط الطول';

  @override
  String get map => 'الخريطة';

  @override
  String get gps => 'نظام تحديد المواقع';

  @override
  String get phone => 'الهاتف';

  @override
  String get mobile => 'الجوال';

  @override
  String get telephone => 'الهاتف';

  @override
  String get fax => 'الفاكس';

  @override
  String get website => 'الموقع الإلكتروني';

  @override
  String get socialMedia => 'وسائل التواصل الاجتماعي';

  @override
  String get facebook => 'فيسبوك';

  @override
  String get twitter => 'تويتر';

  @override
  String get instagram => 'إنستغرام';

  @override
  String get linkedin => 'لينكد إن';

  @override
  String get youtube => 'يوتيوب';

  @override
  String get whatsapp => 'واتساب';

  @override
  String get telegram => 'تيليغرام';

  @override
  String get skype => 'سكايب';

  @override
  String get zoom => 'زوم';

  @override
  String get teams => 'تيمز';

  @override
  String get meet => 'ميت';

  @override
  String get calendar => 'التقويم';

  @override
  String get schedule => 'الجدولة';

  @override
  String get appointment => 'الموعد';

  @override
  String get meeting => 'الاجتماع';

  @override
  String get event => 'الحدث';

  @override
  String get reminder => 'التذكير';

  @override
  String get alarm => 'المنبه';

  @override
  String get timer => 'المؤقت';

  @override
  String get stopwatch => 'ساعة الإيقاف';

  @override
  String get clock => 'الساعة';

  @override
  String get timezone => 'المنطقة الزمنية';

  @override
  String get weather => 'الطقس';

  @override
  String get temperature => 'درجة الحرارة';

  @override
  String get humidity => 'الرطوبة';

  @override
  String get pressure => 'الضغط';

  @override
  String get wind => 'الرياح';

  @override
  String get rain => 'المطر';

  @override
  String get snow => 'الثلج';

  @override
  String get cloud => 'السحابة';

  @override
  String get sun => 'الشمس';

  @override
  String get moon => 'القمر';

  @override
  String get star => 'النجم';

  @override
  String get planet => 'الكوكب';

  @override
  String get earth => 'الأرض';

  @override
  String get space => 'الفضاء';

  @override
  String get universe => 'الكون';

  @override
  String get galaxy => 'المجرة';

  @override
  String get solar => 'شمسي';

  @override
  String get lunar => 'قمري';

  @override
  String get eclipse => 'الكسوف';

  @override
  String get comet => 'المذنب';

  @override
  String get meteor => 'الشهاب';

  @override
  String get asteroid => 'الكويكب';

  @override
  String get satellite => 'القمر الصناعي';

  @override
  String get rocket => 'الصاروخ';

  @override
  String get spacecraft => 'المركبة الفضائية';

  @override
  String get astronaut => 'رائد الفضاء';

  @override
  String get alien => 'الكائن الفضائي';

  @override
  String get ufo => 'الجسم الغريب';
}
