// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'MDD FinTech';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get register => 'Register';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get changePassword => 'Change Password';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get notifications => 'Notifications';

  @override
  String get home => 'Home';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get statistics => 'Statistics';

  @override
  String get reports => 'Reports';

  @override
  String get transactions => 'Transactions';

  @override
  String get investments => 'Investments';

  @override
  String get wallet => 'Wallet';

  @override
  String get balance => 'Balance';

  @override
  String get deposit => 'Deposit';

  @override
  String get withdraw => 'Withdraw';

  @override
  String get transfer => 'Transfer';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get sort => 'Sort';

  @override
  String get refresh => 'Refresh';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Information';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get ok => 'OK';

  @override
  String get retry => 'Retry';

  @override
  String get close => 'Close';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get finish => 'Finish';

  @override
  String get skip => 'Skip';

  @override
  String get done => 'Done';

  @override
  String get continueAction => 'Continue';

  @override
  String get back => 'Back';

  @override
  String get submit => 'Submit';

  @override
  String get send => 'Send';

  @override
  String get receive => 'Receive';

  @override
  String get share => 'Share';

  @override
  String get copy => 'Copy';

  @override
  String get paste => 'Paste';

  @override
  String get cut => 'Cut';

  @override
  String get select => 'Select';

  @override
  String get selectAll => 'Select All';

  @override
  String get clear => 'Clear';

  @override
  String get reset => 'Reset';

  @override
  String get update => 'Update';

  @override
  String get upgrade => 'Upgrade';

  @override
  String get download => 'Download';

  @override
  String get upload => 'Upload';

  @override
  String get import => 'Import';

  @override
  String get export => 'Export';

  @override
  String get print => 'Print';

  @override
  String get preview => 'Preview';

  @override
  String get view => 'View';

  @override
  String get hide => 'Hide';

  @override
  String get show => 'Show';

  @override
  String get enable => 'Enable';

  @override
  String get disable => 'Disable';

  @override
  String get activate => 'Activate';

  @override
  String get deactivate => 'Deactivate';

  @override
  String get online => 'Online';

  @override
  String get offline => 'Offline';

  @override
  String get connected => 'Connected';

  @override
  String get disconnected => 'Disconnected';

  @override
  String get available => 'Available';

  @override
  String get unavailable => 'Unavailable';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get pending => 'Pending';

  @override
  String get approved => 'Approved';

  @override
  String get rejected => 'Rejected';

  @override
  String get completed => 'Completed';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get failed => 'Failed';

  @override
  String get expired => 'Expired';

  @override
  String get valid => 'Valid';

  @override
  String get invalid => 'Invalid';

  @override
  String get required => 'Required';

  @override
  String get optional => 'Optional';

  @override
  String get public => 'Public';

  @override
  String get private => 'Private';

  @override
  String get personal => 'Personal';

  @override
  String get business => 'Business';

  @override
  String get general => 'General';

  @override
  String get advanced => 'Advanced';

  @override
  String get basic => 'Basic';

  @override
  String get premium => 'Premium';

  @override
  String get free => 'Free';

  @override
  String get paid => 'Paid';

  @override
  String get trial => 'Trial';

  @override
  String get demo => 'Demo';

  @override
  String get beta => 'Beta';

  @override
  String get alpha => 'Alpha';

  @override
  String get stable => 'Stable';

  @override
  String get latest => 'Latest';

  @override
  String get newItem => 'New';

  @override
  String get old => 'Old';

  @override
  String get recent => 'Recent';

  @override
  String get popular => 'Popular';

  @override
  String get featured => 'Featured';

  @override
  String get recommended => 'Recommended';

  @override
  String get trending => 'Trending';

  @override
  String get top => 'Top';

  @override
  String get bottom => 'Bottom';

  @override
  String get left => 'Left';

  @override
  String get right => 'Right';

  @override
  String get center => 'Center';

  @override
  String get start => 'Start';

  @override
  String get end => 'End';

  @override
  String get first => 'First';

  @override
  String get last => 'Last';

  @override
  String get all => 'All';

  @override
  String get none => 'None';

  @override
  String get other => 'Other';

  @override
  String get more => 'More';

  @override
  String get less => 'Less';

  @override
  String get today => 'Today';

  @override
  String get yesterday => 'Yesterday';

  @override
  String get tomorrow => 'Tomorrow';

  @override
  String get thisWeek => 'This Week';

  @override
  String get lastWeek => 'Last Week';

  @override
  String get nextWeek => 'Next Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get lastMonth => 'Last Month';

  @override
  String get nextMonth => 'Next Month';

  @override
  String get thisYear => 'This Year';

  @override
  String get lastYear => 'Last Year';

  @override
  String get nextYear => 'Next Year';

  @override
  String get date => 'Date';

  @override
  String get time => 'Time';

  @override
  String get dateTime => 'Date & Time';

  @override
  String get day => 'Day';

  @override
  String get week => 'Week';

  @override
  String get month => 'Month';

  @override
  String get year => 'Year';

  @override
  String get hour => 'Hour';

  @override
  String get minute => 'Minute';

  @override
  String get second => 'Second';

  @override
  String get am => 'AM';

  @override
  String get pm => 'PM';

  @override
  String get language => 'Language';

  @override
  String get theme => 'Theme';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get systemMode => 'System Mode';

  @override
  String get fontSize => 'Font Size';

  @override
  String get small => 'Small';

  @override
  String get medium => 'Medium';

  @override
  String get large => 'Large';

  @override
  String get extraLarge => 'Extra Large';

  @override
  String get currency => 'Currency';

  @override
  String get amount => 'Amount';

  @override
  String get total => 'Total';

  @override
  String get subtotal => 'Subtotal';

  @override
  String get tax => 'Tax';

  @override
  String get discount => 'Discount';

  @override
  String get fee => 'Fee';

  @override
  String get commission => 'Commission';

  @override
  String get interest => 'Interest';

  @override
  String get profit => 'Profit';

  @override
  String get loss => 'Loss';

  @override
  String get income => 'Income';

  @override
  String get expense => 'Expense';

  @override
  String get revenue => 'Revenue';

  @override
  String get cost => 'Cost';

  @override
  String get price => 'Price';

  @override
  String get value => 'Value';

  @override
  String get rate => 'Rate';

  @override
  String get percentage => 'Percentage';

  @override
  String get quantity => 'Quantity';

  @override
  String get count => 'Count';

  @override
  String get number => 'Number';

  @override
  String get id => 'ID';

  @override
  String get name => 'Name';

  @override
  String get title => 'Title';

  @override
  String get description => 'Description';

  @override
  String get details => 'Details';

  @override
  String get summary => 'Summary';

  @override
  String get notes => 'Notes';

  @override
  String get comments => 'Comments';

  @override
  String get message => 'Message';

  @override
  String get subject => 'Subject';

  @override
  String get content => 'Content';

  @override
  String get body => 'Body';

  @override
  String get header => 'Header';

  @override
  String get footer => 'Footer';

  @override
  String get sidebar => 'Sidebar';

  @override
  String get menu => 'Menu';

  @override
  String get navigation => 'Navigation';

  @override
  String get breadcrumb => 'Breadcrumb';

  @override
  String get tab => 'Tab';

  @override
  String get page => 'Page';

  @override
  String get section => 'Section';

  @override
  String get category => 'Category';

  @override
  String get tag => 'Tag';

  @override
  String get label => 'Label';

  @override
  String get badge => 'Badge';

  @override
  String get icon => 'Icon';

  @override
  String get image => 'Image';

  @override
  String get photo => 'Photo';

  @override
  String get picture => 'Picture';

  @override
  String get video => 'Video';

  @override
  String get audio => 'Audio';

  @override
  String get file => 'File';

  @override
  String get document => 'Document';

  @override
  String get folder => 'Folder';

  @override
  String get directory => 'Directory';

  @override
  String get path => 'Path';

  @override
  String get url => 'URL';

  @override
  String get link => 'Link';

  @override
  String get address => 'Address';

  @override
  String get location => 'Location';

  @override
  String get position => 'Position';

  @override
  String get coordinates => 'Coordinates';

  @override
  String get latitude => 'Latitude';

  @override
  String get longitude => 'Longitude';

  @override
  String get map => 'Map';

  @override
  String get gps => 'GPS';

  @override
  String get phone => 'Phone';

  @override
  String get mobile => 'Mobile';

  @override
  String get telephone => 'Telephone';

  @override
  String get fax => 'Fax';

  @override
  String get website => 'Website';

  @override
  String get socialMedia => 'Social Media';

  @override
  String get facebook => 'Facebook';

  @override
  String get twitter => 'Twitter';

  @override
  String get instagram => 'Instagram';

  @override
  String get linkedin => 'LinkedIn';

  @override
  String get youtube => 'YouTube';

  @override
  String get whatsapp => 'WhatsApp';

  @override
  String get telegram => 'Telegram';

  @override
  String get skype => 'Skype';

  @override
  String get zoom => 'Zoom';

  @override
  String get teams => 'Teams';

  @override
  String get meet => 'Meet';

  @override
  String get calendar => 'Calendar';

  @override
  String get schedule => 'Schedule';

  @override
  String get appointment => 'Appointment';

  @override
  String get meeting => 'Meeting';

  @override
  String get event => 'Event';

  @override
  String get reminder => 'Reminder';

  @override
  String get alarm => 'Alarm';

  @override
  String get timer => 'Timer';

  @override
  String get stopwatch => 'Stopwatch';

  @override
  String get clock => 'Clock';

  @override
  String get timezone => 'Timezone';

  @override
  String get weather => 'Weather';

  @override
  String get temperature => 'Temperature';

  @override
  String get humidity => 'Humidity';

  @override
  String get pressure => 'Pressure';

  @override
  String get wind => 'Wind';

  @override
  String get rain => 'Rain';

  @override
  String get snow => 'Snow';

  @override
  String get cloud => 'Cloud';

  @override
  String get sun => 'Sun';

  @override
  String get moon => 'Moon';

  @override
  String get star => 'Star';

  @override
  String get planet => 'Planet';

  @override
  String get earth => 'Earth';

  @override
  String get space => 'Space';

  @override
  String get universe => 'Universe';

  @override
  String get galaxy => 'Galaxy';

  @override
  String get solar => 'Solar';

  @override
  String get lunar => 'Lunar';

  @override
  String get eclipse => 'Eclipse';

  @override
  String get comet => 'Comet';

  @override
  String get meteor => 'Meteor';

  @override
  String get asteroid => 'Asteroid';

  @override
  String get satellite => 'Satellite';

  @override
  String get rocket => 'Rocket';

  @override
  String get spacecraft => 'Spacecraft';

  @override
  String get astronaut => 'Astronaut';

  @override
  String get alien => 'Alien';

  @override
  String get ufo => 'UFO';
}
