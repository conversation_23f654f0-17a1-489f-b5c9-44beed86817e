import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_flavor/flutter_flavor.dart';

import 'core/injection/injection_container.dart';
import 'core/my_app.dart';
import 'core/provider/my_bloc_observer.dart';
import 'firebase_options.dart';
import 'services/fcm.dart';
import 'services/notification_service.dart';
import 'services/storage_service.dart';
import 'utils/config_reader.dart';
import 'utils/constants.dart';
import 'utils/environment.dart';
import 'utils/sentry_reporter.dart';

Future<void> mainCommon(String env) async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await initGetItSetup();

  // Initialize Firebase
 // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  
  // Set up Firebase messaging background handler
 // FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  // Initialize notification service
  if (!kIsWeb) {
   // await setupFlutterNotifications();
  //  await NotificationService().initialize();
  }

  // Initialize storage service
  await StorageService().initialize();

  // Load the JSON config into memory
  await ConfigReader.initialize();

  // Initialize Bloc Observer
  Bloc.observer = MyBlocObserver();

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );

  // Force Portrait Mode
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize Flavor configuration
  FlavorConfig(
    name: env == Environment.prod ? 'Production' : 'Development',
    color: env == Environment.prod ? Colors.red : Colors.green,
    location: BannerLocation.bottomStart,
    variables: <String, dynamic>{
      "flavor": env,
      "baseUrl": env == Environment.prod
          ? AppConstants.kBaseUrl
          : AppConstants.kBaseUrlDebug,
    },
  );

  // Initialize Sentry for error reporting (only in release mode)
  if (kDebugMode) {
    runApp(const MyApp());
  } else {
    await SentryReporter.setup(const MyApp());
  }
}
