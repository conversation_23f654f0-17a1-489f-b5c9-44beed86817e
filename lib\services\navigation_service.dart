import 'package:flutter/material.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static BuildContext? get context => navigatorKey.currentContext;
  static NavigatorState? get navigator => navigatorKey.currentState;

  /// Navigate to a new screen
  static Future<T?> push<T>(Widget page) {
    return navigator!.push<T>(MaterialPageRoute(builder: (_) => page));
  }

  /// Navigate to a new screen with custom route
  static Future<T?> pushRoute<T>(Route<T> route) {
    return navigator!.push<T>(route);
  }

  /// Navigate to a new screen and replace the current one
  static Future<T?> pushReplacement<T>(Widget page) {
    return navigator!.pushReplacement<T, dynamic>(
      MaterialPageRoute(builder: (_) => page),
    );
  }

  /// Navigate to a new screen and clear the entire stack
  static Future<T?> pushAndClearStack<T>(Widget page) {
    return navigator!.pushAndRemoveUntil<T>(
      MaterialPageRoute(builder: (_) => page),
      (route) => false,
    );
  }

  /// Navigate to a new screen and remove routes until predicate
  static Future<T?> pushAndRemoveUntil<T>(
    Widget page,
    bool Function(Route<dynamic>) predicate,
  ) {
    return navigator!.pushAndRemoveUntil<T>(
      MaterialPageRoute(builder: (_) => page),
      predicate,
    );
  }

  /// Pop the current screen
  static void pop<T>([T? result]) {
    if (navigator!.canPop()) {
      navigator!.pop<T>(result);
    }
  }

  /// Pop until a specific route
  static void popUntil(bool Function(Route<dynamic>) predicate) {
    navigator!.popUntil(predicate);
  }

  /// Pop to the first route (clear stack except first)
  static void popToFirst() {
    navigator!.popUntil((route) => route.isFirst);
  }

  /// Check if we can pop
  static bool canPop() {
    return navigator!.canPop();
  }

  /// Show a modal bottom sheet
  static Future<T?> showBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
    Clip? clipBehavior,
    BoxConstraints? constraints,
    Color? barrierColor,
    bool useRootNavigator = false,
  }) {
    return showModalBottomSheet<T>(
      context: context!,
      builder: (_) => child,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      clipBehavior: clipBehavior,
      constraints: constraints,
      barrierColor: barrierColor,
      useRootNavigator: useRootNavigator,
    );
  }

  /// Show a dialog
  static Future<T?> showAppDialog<T>({
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
    bool useRootNavigator = true,
  }) {
    return showDialog<T>(
      context: context!,
      builder: (_) => child,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel,
      useRootNavigator: useRootNavigator,
    );
  }

  /// Show a custom dialog
  static Future<T?> showCustomDialog<T>({
    required Widget child,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
    bool useRootNavigator = true,
    RouteTransitionsBuilder? transitionsBuilder,
    Duration transitionDuration = const Duration(milliseconds: 200),
  }) {
    return showGeneralDialog<T>(
      context: context!,
      pageBuilder: (context, animation, secondaryAnimation) => child,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? Colors.black54,
      barrierLabel: barrierLabel,
      useRootNavigator: useRootNavigator,
      transitionDuration: transitionDuration,
      transitionBuilder:
          transitionsBuilder ??
          (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: ScaleTransition(scale: animation, child: child),
            );
          },
    );
  }

  /// Show a snackbar
  static void showSnackBar({
    required String message,
    Duration duration = const Duration(seconds: 3),
    SnackBarAction? action,
    Color? backgroundColor,
    Color? textColor,
    double? elevation,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    double? width,
    ShapeBorder? shape,
    SnackBarBehavior? behavior,
  }) {
    final scaffoldMessenger = ScaffoldMessenger.of(context!);
    scaffoldMessenger.hideCurrentSnackBar();
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(message, style: TextStyle(color: textColor)),
        duration: duration,
        action: action,
        backgroundColor: backgroundColor,
        elevation: elevation,
        margin: margin,
        padding: padding,
        width: width,
        shape: shape,
        behavior: behavior,
      ),
    );
  }

  /// Hide current snackbar
  static void hideSnackBar() {
    ScaffoldMessenger.of(context!).hideCurrentSnackBar();
  }

  /// Get current route name
  static String? getCurrentRouteName() {
    String? routeName;
    navigator!.popUntil((route) {
      routeName = route.settings.name;
      return true;
    });
    return routeName;
  }

  /// Check if a specific route is in the stack
  static bool isRouteInStack(String routeName) {
    bool found = false;
    navigator!.popUntil((route) {
      if (route.settings.name == routeName) {
        found = true;
      }
      return true;
    });
    return found;
  }
}
