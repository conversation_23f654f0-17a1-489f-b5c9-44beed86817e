import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../utils/sentry_reporter.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  late SharedPreferences _prefs;
  late FlutterSecureStorage _secureStorage;

  /// Initialize storage service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    _secureStorage = const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    );
  }

  // ==================== Shared Preferences ====================

  /// Save string to shared preferences
  Future<bool> saveString(String key, String value) async {
    try {
      return await _prefs.setString(key, value);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Get string from shared preferences
  String? getString(String key, {String? defaultValue}) {
    try {
      return _prefs.getString(key) ?? defaultValue;
    } catch (e) {
      SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return defaultValue;
    }
  }

  /// Save int to shared preferences
  Future<bool> saveInt(String key, int value) async {
    try {
      return await _prefs.setInt(key, value);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Get int from shared preferences
  int? getInt(String key, {int? defaultValue}) {
    try {
      return _prefs.getInt(key) ?? defaultValue;
    } catch (e) {
      SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return defaultValue;
    }
  }

  /// Save double to shared preferences
  Future<bool> saveDouble(String key, double value) async {
    try {
      return await _prefs.setDouble(key, value);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Get double from shared preferences
  double? getDouble(String key, {double? defaultValue}) {
    try {
      return _prefs.getDouble(key) ?? defaultValue;
    } catch (e) {
      SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return defaultValue;
    }
  }

  /// Save bool to shared preferences
  Future<bool> saveBool(String key, bool value) async {
    try {
      return await _prefs.setBool(key, value);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Get bool from shared preferences
  bool? getBool(String key, {bool? defaultValue}) {
    try {
      return _prefs.getBool(key) ?? defaultValue;
    } catch (e) {
      SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return defaultValue;
    }
  }

  /// Save list of strings to shared preferences
  Future<bool> saveStringList(String key, List<String> value) async {
    try {
      return await _prefs.setStringList(key, value);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Get list of strings from shared preferences
  List<String>? getStringList(String key, {List<String>? defaultValue}) {
    try {
      return _prefs.getStringList(key) ?? defaultValue;
    } catch (e) {
      SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return defaultValue;
    }
  }

  /// Save object to shared preferences (as JSON)
  Future<bool> saveObject(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      return await _prefs.setString(key, jsonString);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Get object from shared preferences (from JSON)
  Map<String, dynamic>? getObject(String key) {
    try {
      final jsonString = _prefs.getString(key);
      if (jsonString != null) {
        return json.decode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return null;
    }
  }

  /// Remove key from shared preferences
  Future<bool> remove(String key) async {
    try {
      return await _prefs.remove(key);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Clear all shared preferences
  Future<bool> clear() async {
    try {
      return await _prefs.clear();
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Check if key exists in shared preferences
  bool containsKey(String key) {
    try {
      return _prefs.containsKey(key);
    } catch (e) {
      SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Get all keys from shared preferences
  Set<String> getKeys() {
    try {
      return _prefs.getKeys();
    } catch (e) {
      SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return <String>{};
    }
  }

  // ==================== Secure Storage ====================

  /// Save string to secure storage
  Future<void> saveSecureString(String key, String value) async {
    try {
      await _secureStorage.write(key: key, value: value);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
    }
  }

  /// Get string from secure storage
  Future<String?> getSecureString(String key) async {
    try {
      return await _secureStorage.read(key: key);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return null;
    }
  }

  /// Save object to secure storage (as JSON)
  Future<void> saveSecureObject(String key, Map<String, dynamic> value) async {
    try {
      final jsonString = json.encode(value);
      await _secureStorage.write(key: key, value: jsonString);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
    }
  }

  /// Get object from secure storage (from JSON)
  Future<Map<String, dynamic>?> getSecureObject(String key) async {
    try {
      final jsonString = await _secureStorage.read(key: key);
      if (jsonString != null) {
        return json.decode(jsonString) as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return null;
    }
  }

  /// Remove key from secure storage
  Future<void> removeSecure(String key) async {
    try {
      await _secureStorage.delete(key: key);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
    }
  }

  /// Clear all secure storage
  Future<void> clearSecure() async {
    try {
      await _secureStorage.deleteAll();
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
    }
  }

  /// Check if key exists in secure storage
  Future<bool> containsSecureKey(String key) async {
    try {
      return await _secureStorage.containsKey(key: key);
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Get all keys from secure storage
  Future<Map<String, String>> getAllSecure() async {
    try {
      return await _secureStorage.readAll();
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return <String, String>{};
    }
  }
}
