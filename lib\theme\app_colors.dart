import 'package:flutter/material.dart';

class AppColors {
  // Primary colors (extracted from MDD FinTech logo)
  static const Color primary = Color(0xFF015C8E); // Main blue from logo
  static const Color primaryDark = Color(0xFF014A73); // Darker shade
  static const Color secondary = Color(0xFF9E9E9E); // Gray from logo

  // Neutral colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color darkSilver = Color(0xFF707070);
  static const Color frenchGray = Color(0xFFBDBDC9);
  static const Color mercury = Color(0xFFE1E1E1);
  static const Color metallicSeaweed = Color(0xFF1E8A91);
  static const Color scarpaFlow = Color(0xFF515165);
  static const Color santasGray = Color(0xFF9595AE);
  static const Color manatee = Color(0xFF8E8E9E);

  // Status colors
  static const Color success = Color(0xFF46C695);
  static const Color error = Color(0xFFFF6161);
  static const Color warning = Color(0xFFDEBD7C);
  static const Color info = Color(0xFF5DA1E0);

  // Additional colors (complementing the logo colors)
  static const Color accent = Color(0xFF0078B4); // Lighter blue variant
  static const Color lightBlue = Color(0xFF4A90B8); // Medium blue
  static const Color softGray = Color(0xFFB8B8B8); // Light gray from logo
  static const Color darkGray = Color(0xFF6E6E6E); // Darker gray
  static const Color finTechBlue = Color(0xFF015C8E); // Brand blue

  // Dark theme colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkCard = Color(0xFF2D2D2D);
  static const Color darkText = Color(0xFFE0E0E0);
  static const Color darkTextSecondary = Color(0xFFB0B0B0);

  // Light theme colors
  static const Color lightBackground = Color(0xFFFAFAFA);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightCard = Color(0xFFFFFFFF);
  static const Color lightText = Color(0xFF212121);
  static const Color lightTextSecondary = Color(0xFF757575);
}
