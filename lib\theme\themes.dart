import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'app_colors.dart';

class AppThemes {
  static ThemeData getLightTheme(Locale locale) {
    return ThemeData(
      brightness: Brightness.light,
      // fontFamily: 'DinNextLt', // Commented out until fonts are added
      primarySwatch: MaterialColor(AppColors.primary.value, const {
        50: Color.fromRGBO(1, 92, 142, .1),
        100: Color.fromRGBO(1, 92, 142, .2),
        200: Color.fromRGBO(1, 92, 142, .3),
        300: Color.fromRGBO(1, 92, 142, .4),
        400: Color.fromRGBO(1, 92, 142, .5),
        500: Color.fromRGBO(1, 92, 142, .6),
        600: Color.fromRGBO(1, 92, 142, .7),
        700: Color.fromRGBO(1, 92, 142, .8),
        800: Color.fromRGBO(1, 92, 142, .9),
        900: Color.fromRGBO(1, 92, 142, 1),
      }),
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.lightBackground,
      cardColor: AppColors.lightCard,
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.scarpaFlow,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      inputDecorationTheme: InputDecorationTheme(
        isDense: true,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(50)),
        filled: true,
        fillColor: AppColors.lightSurface,
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
      useMaterial3: false,
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          textStyle: const TextStyle(fontSize: 22, fontWeight: FontWeight.w700),
          shape: const StadiumBorder(),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(shape: const StadiumBorder()),
      ),
      textTheme: const TextTheme().copyWith(
        titleLarge: const TextStyle(
          color: AppColors.lightText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        headlineSmall: const TextStyle(
          fontWeight: FontWeight.w700,
          color: AppColors.lightText,
        ),
        titleMedium: const TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 16,
          color: AppColors.lightText,
        ),
        bodyLarge: const TextStyle(color: AppColors.lightText),
        bodyMedium: const TextStyle(color: AppColors.lightTextSecondary),
      ),
      listTileTheme: ListTileThemeData(
        tileColor: AppColors.lightCard,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
      drawerTheme: DrawerThemeData(
        backgroundColor: AppColors.metallicSeaweed,
        shape: RoundedRectangleBorder(
          borderRadius: locale.languageCode == 'ar'
              ? const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                )
              : const BorderRadius.only(
                  topRight: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
        ),
      ),
      colorScheme: const ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        surface: AppColors.lightSurface,
        background: AppColors.lightBackground,
        error: AppColors.error,
        onPrimary: AppColors.white,
        onSecondary: AppColors.white,
        onSurface: AppColors.lightText,
        onBackground: AppColors.lightText,
        onError: AppColors.white,
      ),
    );
  }

  static ThemeData getDarkTheme(Locale locale) {
    return ThemeData(
      brightness: Brightness.dark,
      // fontFamily: 'DinNextLt', // Commented out until fonts are added
      primarySwatch: MaterialColor(AppColors.primary.value, const {
        50: Color.fromRGBO(1, 92, 142, .1),
        100: Color.fromRGBO(1, 92, 142, .2),
        200: Color.fromRGBO(1, 92, 142, .3),
        300: Color.fromRGBO(1, 92, 142, .4),
        400: Color.fromRGBO(1, 92, 142, .5),
        500: Color.fromRGBO(1, 92, 142, .6),
        600: Color.fromRGBO(1, 92, 142, .7),
        700: Color.fromRGBO(1, 92, 142, .8),
        800: Color.fromRGBO(1, 92, 142, .9),
        900: Color.fromRGBO(1, 92, 142, 1),
      }),
      primaryColor: AppColors.primary,
      scaffoldBackgroundColor: AppColors.darkBackground,
      cardColor: AppColors.darkCard,
      appBarTheme: const AppBarTheme(
        elevation: 0,
        centerTitle: true,
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.darkText,
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      inputDecorationTheme: InputDecorationTheme(
        isDense: true,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(50)),
        filled: true,
        fillColor: AppColors.darkSurface,
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
      useMaterial3: false,
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          textStyle: const TextStyle(fontSize: 22, fontWeight: FontWeight.w700),
          shape: const StadiumBorder(),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(shape: const StadiumBorder()),
      ),
      textTheme: const TextTheme().copyWith(
        titleLarge: const TextStyle(
          color: AppColors.darkText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        headlineSmall: const TextStyle(
          fontWeight: FontWeight.w700,
          color: AppColors.darkText,
        ),
        titleMedium: const TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 16,
          color: AppColors.darkText,
        ),
        bodyLarge: const TextStyle(color: AppColors.darkText),
        bodyMedium: const TextStyle(color: AppColors.darkTextSecondary),
      ),
      listTileTheme: ListTileThemeData(
        tileColor: AppColors.darkCard,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
      drawerTheme: DrawerThemeData(
        backgroundColor: AppColors.darkSurface,
        shape: RoundedRectangleBorder(
          borderRadius: locale.languageCode == 'ar'
              ? const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                )
              : const BorderRadius.only(
                  topRight: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
        ),
      ),
      colorScheme: const ColorScheme.dark(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        surface: AppColors.darkSurface,
        background: AppColors.darkBackground,
        error: AppColors.error,
        onPrimary: AppColors.white,
        onSecondary: AppColors.white,
        onSurface: AppColors.darkText,
        onBackground: AppColors.darkText,
        onError: AppColors.white,
      ),
    );
  }

  static ThemeData getThemeData(Locale locale, bool isDarkMode) {
    return isDarkMode ? getDarkTheme(locale) : getLightTheme(locale);
  }
}
