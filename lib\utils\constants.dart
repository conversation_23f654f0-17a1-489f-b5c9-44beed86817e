class AppConstants {
  static const paginationLimit = 5;
  static const maxPageLimit = 1000;
  static const maxFileSize = 2000000; // in bytes
  static const kBaseUrlDebug = 'http://localhost:7000/api';
  static const kBaseUrl = 'https://api.mddplus.com/api';
  static const kPrivacyPolicy = 'https://www.mdd.sa/terms-and-conditions';
  static final kStartDate = DateTime(DateTime.now().year).toIso8601String();
  static final kEndDate = DateTime.now().toIso8601String();
}

class SecureStorageKeys {
  static const String email = 'EMAIL';
  static const String password = 'PASSWORD';
  static const String token = 'TOKEN';
}

class PrefKeys {
  static const String isEn = 'IS-EN';
  static const String userID = 'USER-ID';
  static const String userData = 'USER-DATA';
  static const String walletID = 'WALLET-ID';
  static const String walletName = 'WALLET-NAME';
  static const String isBiometricEnabled = 'IS-BIOMETRIC-ENABLED';
  static const String isBalanceShown = 'IS-BALANCE-SHOWN';
  static const String isBalancesInHomeHidden = 'IS-BALANCE-IN-HOME-HIDDEN';
  static const String isDarkMode = 'IS-DARK-MODE';
  //------- New Feature
  static const String isHighlightHomeShown = 'IS-HIGHLIGHT-HOME-SHOWN';
  static const String isHighlightAppBarShown = 'IS-HIGHLIGHT-APPBAR-SHOWN';
  static const String isHighlightDrawerShown = 'IS-HIGHLIGHT-DRAWER-SHOWN';
  static const String isHighlightWalletShown = 'IS-HIGHLIGHT-WALLET-SHOWN';
}

class AppKeys {
  static const notificationsListKey = 'notifications_list_scroll';
  static const dueDateSoonListKey = 'due_date_soon_list_scroll';
  static const myInvestmentsListKey = 'my_investments_list_scroll';
  static const myTransactionsListKey = 'my_transactions_list_scroll';
  static const myDepositsListKey = 'my_deposits_list_scroll';
  static const myWithdrawalsListKey = 'my_withdrawals_list_scroll';
  static const opportunitiesListKey = 'opportunities_list_scroll';
  static const realCapitalWalletsListKey = 'real_capital_wallets_list_scroll';
  static const expectedProfitsDebounceKey = 'expected_profits_debounce';
}
