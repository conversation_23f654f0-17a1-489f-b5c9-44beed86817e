import 'package:json_annotation/json_annotation.dart';

enum ToastStates {
  success,
  error,
  warning,
}

enum PlatformTypes {
  @JsonValue("0")
  web,
  @JsonValue("1")
  ios,
  @JsonValue("2")
  android,
}

enum BottomNavTab {
  home,
  statistics,
  settings,
  profile,
}

enum ThemeMode {
  light,
  dark,
  system,
}

enum UserStatus {
  @JsonValue(0)
  pending,
  @JsonValue(1)
  active,
  @JsonValue(2)
  banned,
  @JsonValue(3)
  inactive,
}
