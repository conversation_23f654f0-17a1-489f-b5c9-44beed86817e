import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import 'helper_functions.dart';

/// String extensions
extension StringExtensions on String {
  /// Capitalize first letter
  String get capitalize => HelperFunctions.capitalize(this);

  /// Capitalize each word
  String get capitalizeWords => HelperFunctions.capitalizeWords(this);

  /// Check if string is email
  bool get isEmail => HelperFunctions.isEmail(this);

  /// Check if string is phone number
  bool get isPhoneNumber => HelperFunctions.isPhoneNumber(this);

  /// Check if string is URL
  bool get isUrl => HelperFunctions.isUrl(this);

  /// Remove HTML tags
  String get removeHtmlTags => HelperFunctions.removeHtmlTags(this);

  /// Get initials from name
  String get initials => HelperFunctions.getInitials(this);

  /// Get file extension
  String get fileExtension => HelperFunctions.getFileExtension(this);

  /// Check if file is image
  bool get isImageFile => HelperFunctions.isImageFile(this);

  /// Check if file is video
  bool get isVideoFile => HelperFunctions.isVideoFile(this);

  /// Check if file is audio
  bool get isAudioFile => HelperFunctions.isAudioFile(this);

  /// Check if file is document
  bool get isDocumentFile => HelperFunctions.isDocumentFile(this);

  /// Truncate text
  String truncate(int maxLength, {String suffix = '...'}) =>
      HelperFunctions.truncateText(this, maxLength, suffix: suffix);

  /// Convert to DateTime
  DateTime? get toDateTime {
    try {
      return DateTime.parse(this);
    } catch (e) {
      return null;
    }
  }

  /// Convert to int
  int? get toInt {
    try {
      return int.parse(this);
    } catch (e) {
      return null;
    }
  }

  /// Convert to double
  double? get toDouble {
    try {
      return double.parse(this);
    } catch (e) {
      return null;
    }
  }

  /// Check if string is numeric
  bool get isNumeric => double.tryParse(this) != null;

  /// Check if string is empty or null
  bool get isNullOrEmpty => isEmpty;

  /// Check if string is not empty and not null
  bool get isNotNullOrEmpty => isNotEmpty;

  /// Reverse string
  String get reverse => split('').reversed.join('');

  /// Count words
  int get wordCount => trim().split(RegExp(r'\s+')).length;

  /// Count characters (excluding spaces)
  int get characterCount => replaceAll(' ', '').length;

  /// Convert to title case
  String get toTitleCase => split(' ')
      .map((word) => word.isNotEmpty
          ? word[0].toUpperCase() + word.substring(1).toLowerCase()
          : word)
      .join(' ');

  /// Convert to snake_case
  String get toSnakeCase => replaceAllMapped(
        RegExp(r'[A-Z]'),
        (match) => '_${match.group(0)!.toLowerCase()}',
      ).replaceFirst('_', '');

  /// Convert to camelCase
  String get toCamelCase {
    final words = split('_');
    if (words.isEmpty) return this;
    return words.first.toLowerCase() +
        words.skip(1).map((word) => word.capitalize).join('');
  }

  /// Convert to PascalCase
  String get toPascalCase =>
      split('_').map((word) => word.capitalize).join('');

  /// Convert to kebab-case
  String get toKebabCase => replaceAllMapped(
        RegExp(r'[A-Z]'),
        (match) => '-${match.group(0)!.toLowerCase()}',
      ).replaceFirst('-', '');
}

/// DateTime extensions
extension DateTimeExtensions on DateTime {
  /// Format date to string
  String format(String pattern) => DateFormat(pattern).format(this);

  /// Format date to localized string
  String formatLocalized(String locale) =>
      HelperFunctions.formatDateLocalized(this, locale);

  /// Format time to localized string
  String formatTimeLocalized(String locale) =>
      HelperFunctions.formatTimeLocalized(this, locale);

  /// Format date and time to localized string
  String formatDateTimeLocalized(String locale) =>
      HelperFunctions.formatDateTimeLocalized(this, locale);

  /// Get time ago string
  String timeAgo(String locale) =>
      HelperFunctions.getTimeAgo(this, locale);

  /// Check if date is today
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  /// Check if date is yesterday
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year &&
        month == yesterday.month &&
        day == yesterday.day;
  }

  /// Check if date is tomorrow
  bool get isTomorrow {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return year == tomorrow.year &&
        month == tomorrow.month &&
        day == tomorrow.day;
  }

  /// Check if date is this week
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
        isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  /// Check if date is this month
  bool get isThisMonth {
    final now = DateTime.now();
    return year == now.year && month == now.month;
  }

  /// Check if date is this year
  bool get isThisYear {
    final now = DateTime.now();
    return year == now.year;
  }

  /// Get start of day
  DateTime get startOfDay => DateTime(year, month, day);

  /// Get end of day
  DateTime get endOfDay => DateTime(year, month, day, 23, 59, 59, 999);

  /// Get start of week (Monday)
  DateTime get startOfWeek =>
      subtract(Duration(days: weekday - 1)).startOfDay;

  /// Get end of week (Sunday)
  DateTime get endOfWeek => add(Duration(days: 7 - weekday)).endOfDay;

  /// Get start of month
  DateTime get startOfMonth => DateTime(year, month, 1);

  /// Get end of month
  DateTime get endOfMonth => DateTime(year, month + 1, 0, 23, 59, 59, 999);

  /// Get start of year
  DateTime get startOfYear => DateTime(year, 1, 1);

  /// Get end of year
  DateTime get endOfYear => DateTime(year, 12, 31, 23, 59, 59, 999);

  /// Calculate age
  int get age => HelperFunctions.calculateAge(this);

  /// Add business days
  DateTime addBusinessDays(int days) {
    var result = this;
    var remainingDays = days;

    while (remainingDays > 0) {
      result = result.add(const Duration(days: 1));
      if (result.weekday < 6) {
        // Monday to Friday
        remainingDays--;
      }
    }

    return result;
  }

  /// Check if date is weekend
  bool get isWeekend => weekday == DateTime.saturday || weekday == DateTime.sunday;

  /// Check if date is weekday
  bool get isWeekday => !isWeekend;
}

/// Number extensions
extension NumExtensions on num {
  /// Format as currency
  String toCurrency({String symbol = '\$'}) =>
      HelperFunctions.formatCurrency(toDouble(), symbol: symbol);

  /// Format with commas
  String get formatted => HelperFunctions.formatNumber(this);

  /// Format as percentage
  String toPercentage({int decimalPlaces = 2}) =>
      HelperFunctions.formatPercentage(toDouble(), decimalPlaces: decimalPlaces);

  /// Convert bytes to human readable format
  String get fileSize => HelperFunctions.getFileSizeString(bytes: toInt());

  /// Check if number is positive
  bool get isPositive => this > 0;

  /// Check if number is negative
  bool get isNegative => this < 0;

  /// Check if number is zero
  bool get isZero => this == 0;

  /// Check if number is even
  bool get isEven => toInt() % 2 == 0;

  /// Check if number is odd
  bool get isOdd => toInt() % 2 != 0;

  /// Clamp value between min and max
  num clampValue(num min, num max) => this < min ? min : (this > max ? max : this);

  /// Convert to ordinal string (1st, 2nd, 3rd, etc.)
  String get ordinal {
    final number = toInt();
    if (number >= 11 && number <= 13) {
      return '${number}th';
    }
    switch (number % 10) {
      case 1:
        return '${number}st';
      case 2:
        return '${number}nd';
      case 3:
        return '${number}rd';
      default:
        return '${number}th';
    }
  }
}

/// List extensions
extension ListExtensions<T> on List<T> {
  /// Get random element
  T? get random {
    if (isEmpty) return null;
    return this[HelperFunctions.generateRandomNumber(0, length - 1)];
  }

  /// Check if list is null or empty
  bool get isNullOrEmpty => isEmpty;

  /// Check if list is not null and not empty
  bool get isNotNullOrEmpty => isNotEmpty;

  /// Get first element or null
  T? get firstOrNull => isEmpty ? null : first;

  /// Get last element or null
  T? get lastOrNull => isEmpty ? null : last;

  /// Chunk list into smaller lists
  List<List<T>> chunk(int size) {
    final chunks = <List<T>>[];
    for (var i = 0; i < length; i += size) {
      chunks.add(sublist(i, i + size > length ? length : i + size));
    }
    return chunks;
  }

  /// Remove duplicates
  List<T> get unique => toSet().toList();

  /// Shuffle list
  List<T> get shuffled {
    final list = List<T>.from(this);
    list.shuffle();
    return list;
  }
}

/// BuildContext extensions
extension BuildContextExtensions on BuildContext {
  /// Get screen size
  Size get screenSize => MediaQuery.of(this).size;

  /// Get screen width
  double get screenWidth => screenSize.width;

  /// Get screen height
  double get screenHeight => screenSize.height;

  /// Check if device is tablet
  bool get isTablet => screenWidth >= 768;

  /// Check if device is mobile
  bool get isMobile => screenWidth < 768;

  /// Get theme
  ThemeData get theme => Theme.of(this);

  /// Get text theme
  TextTheme get textTheme => theme.textTheme;

  /// Get color scheme
  ColorScheme get colorScheme => theme.colorScheme;

  /// Get primary color
  Color get primaryColor => colorScheme.primary;

  /// Get secondary color
  Color get secondaryColor => colorScheme.secondary;

  /// Get background color
  Color get backgroundColor => colorScheme.background;

  /// Get surface color
  Color get surfaceColor => colorScheme.surface;

  /// Get error color
  Color get errorColor => colorScheme.error;

  /// Show snackbar
  void showSnackBar(String message, {Duration? duration}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: duration ?? const Duration(seconds: 3),
      ),
    );
  }

  /// Hide keyboard
  void hideKeyboard() {
    FocusScope.of(this).unfocus();
  }

  /// Navigate to page
  Future<T?> push<T>(Widget page) {
    return Navigator.of(this).push<T>(
      MaterialPageRoute(builder: (_) => page),
    );
  }

  /// Navigate and replace current page
  Future<T?> pushReplacement<T>(Widget page) {
    return Navigator.of(this).pushReplacement<T, dynamic>(
      MaterialPageRoute(builder: (_) => page),
    );
  }

  /// Navigate and clear stack
  Future<T?> pushAndClearStack<T>(Widget page) {
    return Navigator.of(this).pushAndRemoveUntil<T>(
      MaterialPageRoute(builder: (_) => page),
      (route) => false,
    );
  }

  /// Pop current page
  void pop<T>([T? result]) {
    Navigator.of(this).pop<T>(result);
  }

  /// Check if can pop
  bool get canPop => Navigator.of(this).canPop();
}
