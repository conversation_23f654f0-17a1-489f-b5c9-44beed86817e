import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_native_html_to_pdf/flutter_native_html_to_pdf.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:uuid/uuid.dart';

import 'constants.dart';
import 'sentry_reporter.dart';

class HelperFunctions {
  /// format date to yyyy/MM/dd
  static String formatDate(String? date) {
    return date == null
        ? ''
        : DateFormat('yyyy/MM/dd').format(DateTime.parse(date));
  }

  /// format date with custom pattern
  static String formatDateWithPattern(String? date, String pattern) {
    return date == null
        ? ''
        : DateFormat(pattern).format(DateTime.parse(date));
  }

  /// format date to localized string
  static String formatDateLocalized(DateTime date, String locale) {
    return DateFormat.yMMMd(locale).format(date);
  }

  /// format time to localized string
  static String formatTimeLocalized(DateTime date, String locale) {
    return DateFormat.jm(locale).format(date);
  }

  /// format date and time to localized string
  static String formatDateTimeLocalized(DateTime date, String locale) {
    return DateFormat.yMMMd(locale).add_jm().format(date);
  }

  /// generate ID
  static String generateId() => const Uuid().v1();

  /// generate UUID v4
  static String generateUuid() => const Uuid().v4();

  /// check if attachment size is allowed
  static bool isAttachmentAllowed(int size) {
    return size <= AppConstants.maxFileSize;
  }

  /// get file size
  static String getFileSizeString({required int bytes, int decimals = 0}) {
    const num = 1024;
    const suffixes = ["B", "KB", "MB", "GB", "TB"];
    final i = (log(bytes) / log(1024)).floor();

    return ((bytes / pow(num, i)).toStringAsFixed(decimals)) + suffixes[i];
  }

  /// Generate pdf from html string
  static String getTimeAgo(
    DateTime dateTime,
    String locale, {
    bool addTimeZone = false,
  }) {
    timeago.setLocaleMessages(
      locale,
      locale == "ar" ? timeago.ArMessages() : timeago.EnMessages(),
    );

    return timeago.format(
      addTimeZone ? dateTime.add(dateTime.timeZoneOffset) : dateTime,
      locale: locale,
    );
  }

  /// Create pdf from list of files
  static Future<pw.Document> createPDF(List<File> files) async {
    final pdf = pw.Document();
    for (final file in files) {
      final image = pw.MemoryImage(file.readAsBytesSync());
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context ctx) {
            return pw.Center(
              child: pw.Image(image),
            );
          },
        ),
      );
    }

    return pdf;
  }

  /// Convert file to base64
  static String convertFileToBase64(String path) {
    return base64Encode(File(path).readAsBytesSync());
  }

  /// Convert Html to file
  static Future<File?> generatePdf(String htmlContent) async {
    try {
      final appDocDir = await getApplicationDocumentsDirectory();

      return FlutterNativeHtmlToPdf().convertHtmlToPdf(
        html: htmlContent,
        targetDirectory: appDocDir.path,
        targetName: "document-${generateId()}",
      );
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return null;
    }
  }

  /// Print pdf
  static Future<bool> printPdf(String html) async {
    try {
      return Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => Printing.convertHtml(
          format: format,
          html: html,
        ),
        format: PdfPageFormat.a4,
        name: "document-${generateId()}",
      );
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return false;
    }
  }

  /// Pick files
  static Future<List<File>?> pickFiles({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
    bool allowMultiple = false,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: allowMultiple,
      );

      if (result != null) {
        return result.paths.map((path) => File(path!)).toList();
      }
      return null;
    } catch (e) {
      await SentryReporter.genericThrow(e, stackTrace: StackTrace.current);
      return null;
    }
  }

  /// Pick single file
  static Future<File?> pickSingleFile({
    FileType type = FileType.any,
    List<String>? allowedExtensions,
  }) async {
    final files = await pickFiles(
      type: type,
      allowedExtensions: allowedExtensions,
      allowMultiple: false,
    );
    return files?.isNotEmpty == true ? files!.first : null;
  }

  /// Format currency
  static String formatCurrency(double amount, {String symbol = '\$'}) {
    final formatter = NumberFormat.currency(symbol: symbol);
    return formatter.format(amount);
  }

  /// Format number with commas
  static String formatNumber(num number) {
    final formatter = NumberFormat('#,##0');
    return formatter.format(number);
  }

  /// Format percentage
  static String formatPercentage(double value, {int decimalPlaces = 2}) {
    final formatter = NumberFormat.percentPattern();
    formatter.minimumFractionDigits = decimalPlaces;
    formatter.maximumFractionDigits = decimalPlaces;
    return formatter.format(value / 100);
  }

  /// Capitalize first letter
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  /// Capitalize each word
  static String capitalizeWords(String text) {
    return text.split(' ').map((word) => capitalize(word)).join(' ');
  }

  /// Truncate text
  static String truncateText(String text, int maxLength, {String suffix = '...'}) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - suffix.length) + suffix;
  }

  /// Remove HTML tags
  static String removeHtmlTags(String htmlText) {
    final RegExp exp = RegExp(r"<[^>]*>", multiLine: true, caseSensitive: true);
    return htmlText.replaceAll(exp, '');
  }

  /// Generate random string
  static String generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }

  /// Generate random number
  static int generateRandomNumber(int min, int max) {
    final random = Random();
    return min + random.nextInt(max - min + 1);
  }

  /// Check if string is email
  static bool isEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Check if string is phone number
  static bool isPhoneNumber(String phone) {
    return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(phone);
  }

  /// Check if string is URL
  static bool isUrl(String url) {
    return RegExp(r'^https?:\/\/[\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?$')
        .hasMatch(url);
  }

  /// Debounce function
  static void debounce(String key, Function() action, {Duration delay = const Duration(milliseconds: 500)}) {
    // Implementation would require a static map to store timers
    // This is a simplified version
    Future.delayed(delay, action);
  }

  /// Get initials from name
  static String getInitials(String name) {
    final words = name.trim().split(' ');
    if (words.isEmpty) return '';
    if (words.length == 1) return words[0][0].toUpperCase();
    return (words[0][0] + words[words.length - 1][0]).toUpperCase();
  }

  /// Calculate age from date of birth
  static int calculateAge(DateTime dateOfBirth) {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  /// Get file extension
  static String getFileExtension(String fileName) {
    return fileName.split('.').last.toLowerCase();
  }

  /// Check if file is image
  static bool isImageFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension);
  }

  /// Check if file is video
  static bool isVideoFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'].contains(extension);
  }

  /// Check if file is audio
  static bool isAudioFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['mp3', 'wav', 'aac', 'ogg', 'wma', 'flac'].contains(extension);
  }

  /// Check if file is document
  static bool isDocumentFile(String fileName) {
    final extension = getFileExtension(fileName);
    return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].contains(extension);
  }
}
