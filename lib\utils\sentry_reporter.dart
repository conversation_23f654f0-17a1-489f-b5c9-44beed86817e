import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_flavor/flutter_flavor.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'config_reader.dart';

class SentryReporter {
  static Future<void> setup(Widget child) async {
    await SentryFlutter.init(
      (options) => options
        ..dsn = ConfigReader.getSentryDns()
        //we recommend lowering this value in production and use tracesSampler (Sentry Docs)
        ..tracesSampleRate = 1.0
        ..reportPackages = false
        ..considerInAppFramesByDefault = false
        ..attachScreenshot = true
        ..sendDefaultPii = true
        ..enableNdkScopeSync = true
        ..environment = FlavorConfig.instance.name,
      appRunner: () => runApp(
        SentryScreenshotWidget(
          child: SentryUserInteractionWidget(
            child: DefaultAssetBundle(
              bundle: SentryAssetBundle(enableStructuredDataTracing: true),
              child: child,
            ),
          ),
        ),
      ),
    );
  }

  static Future<void> genericThrow(dynamic exception,
      {dynamic stackTrace}) async {
    if (kDebugMode) {
      log('SentryReporter: $exception');
    } else {
      if (stackTrace != null) {
        await Sentry.captureException(exception, stackTrace: stackTrace);
      } else {
        await Sentry.captureException(exception);
      }
    }
  }

  static Future<void> captureMessage(String message) async {
    if (kDebugMode) {
      log('SentryReporter Message: $message');
    } else {
      await Sentry.captureMessage(message);
    }
  }

  static Future<void> addBreadcrumb(String message, {String? category}) async {
    await Sentry.addBreadcrumb(
      Breadcrumb(
        message: message,
        category: category,
        timestamp: DateTime.now(),
      ),
    );
  }
}
