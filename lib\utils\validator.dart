import 'package:email_validator/email_validator.dart';

class Validator {
  /// Validate email
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!EmailValidator.validate(value)) {
      return 'Please enter a valid email';
    }
    return null;
  }

  /// Validate password
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (!value.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!value.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!value.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }
    if (!value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return 'Password must contain at least one special character';
    }
    return null;
  }

  /// Validate confirm password
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'Confirm password is required';
    }
    if (value != password) {
      return 'Passwords do not match';
    }
    return null;
  }

  /// Validate required field
  static String? validateRequired(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    return null;
  }

  /// Validate phone number
  static String? validatePhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    // Remove all non-digit characters
    final digitsOnly = value.replaceAll(RegExp(r'\D'), '');
    
    // Check if it's a valid length (10-15 digits)
    if (digitsOnly.length < 10 || digitsOnly.length > 15) {
      return 'Please enter a valid phone number';
    }
    
    // Check if it starts with a valid country code or area code
    if (!RegExp(r'^(\+?[1-9]\d{0,3})?[1-9]\d{8,14}$').hasMatch(value)) {
      return 'Please enter a valid phone number';
    }
    
    return null;
  }

  /// Validate Saudi phone number
  static String? validateSaudiPhoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    
    // Remove all non-digit characters
    final digitsOnly = value.replaceAll(RegExp(r'\D'), '');
    
    // Check Saudi phone number format (05xxxxxxxx or 9665xxxxxxxx)
    if (!RegExp(r'^(05\d{8}|9665\d{8})$').hasMatch(digitsOnly)) {
      return 'Please enter a valid Saudi phone number';
    }
    
    return null;
  }

  /// Validate name
  static String? validateName(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'Name'} is required';
    }
    if (value.trim().length < 2) {
      return '${fieldName ?? 'Name'} must be at least 2 characters';
    }
    if (!RegExp(r'^[a-zA-Z\u0600-\u06FF\s]+$').hasMatch(value.trim())) {
      return '${fieldName ?? 'Name'} can only contain letters and spaces';
    }
    return null;
  }

  /// Validate Arabic name
  static String? validateArabicName(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'Name'} is required';
    }
    if (value.trim().length < 2) {
      return '${fieldName ?? 'Name'} must be at least 2 characters';
    }
    if (!RegExp(r'^[\u0600-\u06FF\s]+$').hasMatch(value.trim())) {
      return '${fieldName ?? 'Name'} must be in Arabic';
    }
    return null;
  }

  /// Validate English name
  static String? validateEnglishName(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'Name'} is required';
    }
    if (value.trim().length < 2) {
      return '${fieldName ?? 'Name'} must be at least 2 characters';
    }
    if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(value.trim())) {
      return '${fieldName ?? 'Name'} must be in English';
    }
    return null;
  }

  /// Validate national ID (Saudi)
  static String? validateSaudiNationalId(String? value) {
    if (value == null || value.isEmpty) {
      return 'National ID is required';
    }
    
    // Remove all non-digit characters
    final digitsOnly = value.replaceAll(RegExp(r'\D'), '');
    
    // Check if it's exactly 10 digits
    if (digitsOnly.length != 10) {
      return 'National ID must be 10 digits';
    }
    
    // Check if it starts with 1 or 2
    if (!digitsOnly.startsWith('1') && !digitsOnly.startsWith('2')) {
      return 'National ID must start with 1 or 2';
    }
    
    // Validate using Luhn algorithm
    if (!_isValidLuhn(digitsOnly)) {
      return 'Please enter a valid National ID';
    }
    
    return null;
  }

  /// Validate IBAN
  static String? validateIBAN(String? value) {
    if (value == null || value.isEmpty) {
      return 'IBAN is required';
    }
    
    // Remove spaces and convert to uppercase
    final iban = value.replaceAll(' ', '').toUpperCase();
    
    // Check Saudi IBAN format (SA followed by 22 digits)
    if (!RegExp(r'^SA\d{22}$').hasMatch(iban)) {
      return 'Please enter a valid Saudi IBAN (SA followed by 22 digits)';
    }
    
    return null;
  }

  /// Validate amount
  static String? validateAmount(String? value, {double? minAmount, double? maxAmount}) {
    if (value == null || value.isEmpty) {
      return 'Amount is required';
    }
    
    final amount = double.tryParse(value);
    if (amount == null) {
      return 'Please enter a valid amount';
    }
    
    if (amount <= 0) {
      return 'Amount must be greater than zero';
    }
    
    if (minAmount != null && amount < minAmount) {
      return 'Amount must be at least $minAmount';
    }
    
    if (maxAmount != null && amount > maxAmount) {
      return 'Amount cannot exceed $maxAmount';
    }
    
    return null;
  }

  /// Validate URL
  static String? validateUrl(String? value) {
    if (value == null || value.isEmpty) {
      return 'URL is required';
    }
    
    if (!RegExp(r'^https?:\/\/[\w\-]+(\.[\w\-]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?$')
        .hasMatch(value)) {
      return 'Please enter a valid URL';
    }
    
    return null;
  }

  /// Validate date of birth
  static String? validateDateOfBirth(DateTime? value, {int? minAge, int? maxAge}) {
    if (value == null) {
      return 'Date of birth is required';
    }
    
    final now = DateTime.now();
    final age = now.year - value.year;
    
    if (value.isAfter(now)) {
      return 'Date of birth cannot be in the future';
    }
    
    if (minAge != null && age < minAge) {
      return 'You must be at least $minAge years old';
    }
    
    if (maxAge != null && age > maxAge) {
      return 'Age cannot exceed $maxAge years';
    }
    
    return null;
  }

  /// Validate OTP
  static String? validateOTP(String? value, {int length = 6}) {
    if (value == null || value.isEmpty) {
      return 'OTP is required';
    }
    
    if (value.length != length) {
      return 'OTP must be $length digits';
    }
    
    if (!RegExp(r'^\d+$').hasMatch(value)) {
      return 'OTP must contain only numbers';
    }
    
    return null;
  }

  /// Validate file size
  static String? validateFileSize(int? sizeInBytes, {int? maxSizeInMB}) {
    if (sizeInBytes == null) {
      return 'File size is required';
    }
    
    final maxSizeInBytes = (maxSizeInMB ?? 5) * 1024 * 1024; // Default 5MB
    
    if (sizeInBytes > maxSizeInBytes) {
      return 'File size cannot exceed ${maxSizeInMB ?? 5}MB';
    }
    
    return null;
  }

  /// Validate file extension
  static String? validateFileExtension(String? fileName, List<String> allowedExtensions) {
    if (fileName == null || fileName.isEmpty) {
      return 'File name is required';
    }
    
    final extension = fileName.split('.').last.toLowerCase();
    
    if (!allowedExtensions.contains(extension)) {
      return 'Only ${allowedExtensions.join(', ')} files are allowed';
    }
    
    return null;
  }

  /// Helper method to validate using Luhn algorithm
  static bool _isValidLuhn(String number) {
    int sum = 0;
    bool alternate = false;
    
    for (int i = number.length - 1; i >= 0; i--) {
      int digit = int.parse(number[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 == 0;
  }

  /// Combine multiple validators
  static String? Function(String?) combine(List<String? Function(String?)> validators) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) return result;
      }
      return null;
    };
  }
}
