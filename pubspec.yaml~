# The name of the project
name: mdd_plus_app

# A brief description of the project
description: A mobile application for managing personal finances with clean architecture.

# The location where the package should be published
publish_to: 'none'

# The version of the package
version: 1.0.0+1

# The minimum required version of the Dart SDK
environment:
  sdk: '>=3.4.3 <4.0.0'

# The dependencies required for the project, organized into categories

# Flutter SDK
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter


# Remote API
  dio: ^5.5.0+1
  internet_connection_checker: ^1.0.0+1
  json_annotation: ^4.9.0

# Firebase
  firebase_core: ^3.10.1
  firebase_analytics: ^11.4.1
  firebase_messaging: ^15.2.1
  flutter_local_notifications: ^18.0.1

# UI
  cached_network_image: ^3.4.1
  cupertino_icons: ^1.0.8
  flutter_easyloading: ^3.0.5
  flutter_html: ^3.0.0-beta.2
  flutter_html_table: ^3.0.0-beta.2
  flutter_svg: ^2.0.10+1
  fluttertoast: ^8.2.6
  percent_indicator: ^4.2.3
  pin_code_fields: ^8.0.1
  rflutter_alert:
    git:
      url: https://github.com/ahmedsabahi/rflutter_alert.git
      ref: master
  share_plus: ^10.1.4
  upgrader: ^10.3.0
  syncfusion_flutter_core: ^28.2.4
  syncfusion_flutter_datepicker: ^28.2.4
  timelines_plus: ^1.0.6
  carousel_slider: ^4.2.1
  syncfusion_flutter_charts: ^28.2.4
  fl_chart: ^0.68.0

# Value equality
  equatable: ^2.0.5

# Local cache
  flutter_secure_storage: ^9.2.2
  local_auth: ^2.2.0
  shared_preferences: ^2.2.3

# Service locator
  get_it: ^7.7.0

# Device & app info
  device_info_plus: ^10.1.0
  package_info_plus: ^8.0.0

# Picker
  file_picker: ^8.0.6

# Bloc for state management
  bloc: ^8.1.4
  flutter_bloc: ^8.1.6

# Functional programming thingies
  dartz: ^0.10.1

# Debugging and testing
  flutter_flavor: ^3.1.3
  sentry_flutter: ^8.3.0
  sentry_dio: ^8.3.0

# Other
  email_validator: ^3.0.0
  intl: ^0.19.0
  mime: ^1.0.4
  path_provider: ^2.1.3
  pdf: ^3.11.0
  printing: ^5.13.1
  timeago: ^3.7.0
  uuid: ^4.5.1
  url_launcher: ^6.3.0
  easy_debounce: ^2.0.3

  flutter_native_html_to_pdf: ^1.0.1

# The development dependencies required for the project
dev_dependencies:
  flutter_test:
    sdk: flutter
  lint: ^2.3.0
  very_good_analysis: ^6.0.0
  flutter_lints: ^4.0.0
# solid_lints: ^0.3.0  # Temporarily disabled due to path dependency conflict
  analyzer: ^7.4.5

  json_serializable: ^6.8.0
  build_runner: ^2.4.11


# Flutter-specific configuration
flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/images/
    - assets/svgs/
    - config/

  # fonts:
  #   - family: DinNextLt
  #     fonts:
  #       - asset: assets/fonts/din-next-lt-w23-bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/din-next-lt-w23-medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/din-next-lt-w23-regular.ttf
  #         weight: 400
  #       - asset: assets/fonts/din-next-lt-w23-light.ttf
  #         weight: 300
